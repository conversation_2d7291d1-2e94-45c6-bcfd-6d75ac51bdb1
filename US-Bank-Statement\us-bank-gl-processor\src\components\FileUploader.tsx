import React, { useState, useCallback, memo, useEffect } from 'react';
import type { ChangeEvent } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Grid,
  Alert,
  CircularProgress
} from '@mui/material';
import { UploadFile, FolderOpen, Download, PictureAsPdf } from '@mui/icons-material';

interface FileUploaderProps {
  onFileProcessed?: (fileName: string, fileSize?: string) => void;
  isDownloadState?: boolean;
  onReset?: () => void;
}

// Add to global type declaration
declare global {
  // Use Window interface augmentation for globalThis
  interface Window {
    fileDetailsForDownload?: { status: string };
    downloadSelectedHistoryItemPDF?: () => void;
    downloadSelectedHistoryItem?: () => void;
    refreshSystemHistory?: () => void;
  }
}

// Use memo to prevent unnecessary re-renders
const FileUploader: React.FC<FileUploaderProps> = memo(({ onFileProcessed, isDownloadState = false, onReset }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isFileUploaded, setIsFileUploaded] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  // This function will be set by the parent to allow FileUploader to trigger a system history refresh
  useEffect(() => {
    // Do not overwrite if already set by parent
    return () => {
      // No-op: parent manages window.refreshSystemHistory
    };
  }, []);

  // Use useCallback to memoize event handlers
  const handleFileChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
      setError(null);
    }
  }, []);

  const handleReset = useCallback(() => {
    setSelectedFile(null);
    setIsFileUploaded(false);
    setError(null);

    // Call the onReset callback if provided
    if (onReset) {
      onReset();
    }
  }, [onReset]);

  // Handle download button click
  const handleDownload = useCallback(async () => {
    console.log('Download button clicked');

    try {
      if (isDownloadState) {
        // If we're in download state (viewing a history item), trigger the download from the parent component
        // The parent component (LandingPage) will handle the download of the selected history item
        if (window.downloadSelectedHistoryItem) {
          window.downloadSelectedHistoryItem();
        } else {
          // Fallback if the global function is not available
          alert('Please use the download button in the history panel to download this file.');
        }
      } else if (selectedFile) {
        // For newly processed files, we'll try to download again from the API
        const formData = new FormData();
        formData.append('file', selectedFile);

        // Send the file to the API server again
        const response = await fetch('http://192.168.1.15:5000/api/process', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to download file: ${response.status} ${response.statusText}`);
        }

        // Get the response as a blob
        const blob = await response.blob();

        // Create a download link for the Excel file
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${selectedFile.name.replace(/\.pdf$/i, '')}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log('File downloaded successfully');
      } else {
        // No file selected
        alert('No file available to download. Please process a file first.');
      }
    } catch (err) {
      console.error('Error in download handler:', err);
      alert(`Download failed: ${err instanceof Error ? err.message : 'Unknown error'}. Please try again.`);
    }
  }, [isDownloadState, selectedFile]);

  const handleUpload = useCallback(async () => {
    // Validate input and show error if needed
    if (!selectedFile) {
      setError('Please select a file before proceeding');
      return;
    }

    // Clear any previous errors
    setError(null);
    setIsProcessing(true);

    // Set a global flag to indicate processing is happening
    // This helps with the refresh warning
    window.fileDetailsForDownload = { status: 'Under Processing' };

    // Log the processing state for debugging
    console.log("Setting processing state to: Under Processing");

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', selectedFile);

      // Log what we're processing
      console.log('Processing selected file:', selectedFile.name);

      // Send the file to the API server
      const response = await fetch('http://192.168.1.15:5000/api/process', {
        method: 'POST',
        body: formData,
      });

      // After 2 seconds, refresh the system history to get the new file
      setTimeout(() => {
        if (typeof window.refreshSystemHistory === 'function') {
          window.refreshSystemHistory();
        }
      }, 2000);

      // Check if the response is successful
      if (!response.ok) {
        // Try to get error message from response
        let errorMessage = 'Failed to process file';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch {
          // If we can't parse the error, use the default message
        }
        throw new Error(errorMessage);
      }

      // Get the response as a blob
      const blob = await response.blob();

      // Create a download link for the Excel file
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${selectedFile.name.replace(/\.pdf$/i, '')}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Set file as uploaded
      setIsFileUploaded(true);

      // Calculate file size in KB or MB for display
      const fileSize = selectedFile.size < 1024 * 1024
        ? `${Math.round(selectedFile.size / 1024)} KB`
        : `${(selectedFile.size / (1024 * 1024)).toFixed(2)} MB`;

      // Notify parent component that a file has been processed
      if (onFileProcessed) {
        onFileProcessed(selectedFile.name, fileSize);
      }
    } catch (err) {
      console.error('Error uploading file:', err);
      setError(err instanceof Error ? err.message : 'Failed to process file');
      setIsFileUploaded(false);

      // Update global state to indicate processing is done (with error)
      window.fileDetailsForDownload = { status: 'Not Available' };
    } finally {
      setIsProcessing(false);
    }
  }, [selectedFile, setError, onFileProcessed]);

  return (
    <Paper
      elevation={3}
      sx={{
        p: 3,
        width: '100%',
        mx: 'auto',
        borderRadius: 2,
        position: 'relative',
        overflow: 'hidden',
        mb: 3,
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '6px',
          background: (theme) =>
            theme.palette.mode === 'light'
              ? `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              : `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
        }
      }}
    >
      <Typography
        variant="h5"
        component="h2"
        gutterBottom
        sx={{
          mb: 3,
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          '&::after': {
            content: '""',
            display: 'block',
            height: '2px',
            background: (theme) =>
              `linear-gradient(90deg, ${theme.palette.primary.main}80, transparent)`,
            flex: 1,
            ml: 2
          }
        }}
      >
        {isDownloadState ? 'Bank Statement Details' : 'Upload Bank Statement'}
      </Typography>

      <Box
        sx={{
          mb: 4,
          p: 3,
          border: '2px dashed',
          borderColor: isProcessing ? 'info.main' : 'divider',
          borderRadius: 2,
          textAlign: 'center',
          transition: 'all 0.2s ease',
          display: isDownloadState ? 'none' : 'block', // Hide the upload box in download state
          backgroundColor: isProcessing ? 'rgba(3, 169, 244, 0.04)' : 'background.paper',
          position: 'relative',
          '&:hover': {
            borderColor: isProcessing ? 'info.main' : 'primary.main',
            backgroundColor: (theme) =>
              isProcessing
                ? 'rgba(3, 169, 244, 0.04)'
                : (theme.palette.mode === 'light'
                  ? 'rgba(25, 118, 210, 0.04)'
                  : 'rgba(33, 150, 243, 0.08)'),
          }
        }}
      >
        <input
          accept=".pdf"
          style={{ display: 'none' }}
          id="file-upload"
          type="file"
          onChange={handleFileChange}
        />
        {isProcessing && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: (theme) =>
                theme.palette.mode === 'light'
                  ? 'rgba(255, 255, 255, 0.8)'
                  : 'rgba(0, 0, 0, 0.8)',
              zIndex: 10,
              borderRadius: 1
            }}
          >
            <CircularProgress size={60} color="info" sx={{ mb: 2 }} />
            <Typography variant="h6" color="info.main" fontWeight="bold">
              Processing File...
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Please wait. This may take 10-15 minutes.
            </Typography>
            <Typography variant="body2" color="error.main" sx={{ mt: 2 }}>
              Do not refresh or close this page
            </Typography>
          </Box>
        )}

        <label htmlFor="file-upload" style={{ width: '100%', cursor: isProcessing ? 'default' : 'pointer' }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <FolderOpen sx={{ fontSize: 48, color: isProcessing ? 'info.main' : 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Drag & Drop Files Here
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              or
            </Typography>
            <Button
              variant="outlined"
              component="span"
              disabled={isProcessing}
              sx={{
                mt: 1,
                borderRadius: 4,
                px: 3,
                py: 1,
                fontWeight: 600,
                borderWidth: 2,
                transition: 'all 0.2s ease',
                '&:hover': {
                  borderWidth: 2,
                  backgroundColor: (theme) =>
                    theme.palette.mode === 'light'
                      ? 'rgba(10, 75, 148, 0.04)'
                      : 'rgba(33, 150, 243, 0.08)',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
                }
              }}
            >
              Browse Files
            </Button>
          </Box>
        </label>

        {selectedFile && (
          <Box
            sx={{
              mt: 2,
              p: 1,
              borderRadius: 1,
              bgcolor: 'background.default',
              border: '1px solid',
              borderColor: 'divider'
            }}
          >
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Selected: {selectedFile.name}
            </Typography>
          </Box>
        )}

        <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 2 }}>
          Supported file format: PDF
        </Typography>
      </Box>

      {error && !isDownloadState && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            animation: 'fadeIn 0.3s ease-in-out'
          }}
        >
          {error}
        </Alert>
      )}

      {(isFileUploaded || isDownloadState) && (
        <Box
          sx={{
            mb: 3,
            mt: 2,
            animation: 'slideUp 0.4s ease-out'
          }}
        >

          <Box
            sx={{
              display: 'flex',
              gap: 2, // Use gap for spacing between buttons
              alignItems: 'center',
              mt: 3
            }}
          >
            <Button
              variant="outlined"
              color="primary"
              onClick={handleReset}
              sx={{
                borderRadius: 4,
                px: 3,
                fontWeight: 600,
                borderWidth: 2,
                transition: 'all 0.2s ease',
                '&:hover': {
                  borderWidth: 2,
                  backgroundColor: (theme) =>
                    theme.palette.mode === 'light'
                      ? 'rgba(10, 75, 148, 0.04)'
                      : 'rgba(33, 150, 243, 0.08)',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
                }
              }}
            >
              Process Another File
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<PictureAsPdf sx={{ color: '#ffffff' }} />}
              onClick={async () => {
                // Download PDF logic
                if (
                  isDownloadState &&
                  typeof window.downloadSelectedHistoryItemPDF === 'function'
                ) {
                  window.downloadSelectedHistoryItemPDF();
                } else if (selectedFile) {
                  const url = window.URL.createObjectURL(selectedFile);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = selectedFile.name;
                  a.click();
                  window.URL.revokeObjectURL(url);
                } else {
                  alert('No PDF file available to download.');
                }
              }}
              size="large"
              sx={{
                borderRadius: 4,
                px: 3,
                color: '#ffffff',
                fontWeight: 600,
                background: (theme) =>
                  theme.palette.mode === 'light'
                    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
                    : `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
                boxShadow: (theme) =>
                  theme.palette.mode === 'light'
                    ? '0 4px 14px rgba(10, 75, 148, 0.3)'
                    : '0 4px 14px rgba(33, 150, 243, 0.3)',
                '&:hover': {
                  background: (theme) =>
                    theme.palette.mode === 'light'
                      ? `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`
                      : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
                  boxShadow: (theme) =>
                    theme.palette.mode === 'light'
                      ? '0 6px 20px rgba(10, 75, 148, 0.4)'
                      : '0 6px 20px rgba(33, 150, 243, 0.4)'
                },
                '&.Mui-disabled': {
                  background: (theme) =>
                    theme.palette.mode === 'light'
                      ? 'rgba(0, 0, 0, 0.12)'
                      : 'rgba(255, 255, 255, 0.12)',
                  color: (theme) =>
                    theme.palette.mode === 'light'
                      ? 'rgba(0, 0, 0, 0.26)'
                      : 'rgba(255, 255, 255, 0.3)',
                  boxShadow: 'none'
                }
              }}
              // PDF download should always be enabled
              disabled={false}
            >
              Download PDF
            </Button>
            <Button
              variant="contained"
              color="secondary"
              startIcon={<Download sx={{ color: '#ffffff' }} />}
              onClick={handleDownload}
              size="large"
              sx={{
                borderRadius: 4,
                px: 3,
                color: '#ffffff',
                fontWeight: 600,
                background: (theme) =>
                  theme.palette.mode === 'light'
                    ? `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`
                    : `linear-gradient(135deg, ${theme.palette.secondary.light} 0%, ${theme.palette.secondary.main} 100%)`,
                boxShadow: (theme) =>
                  theme.palette.mode === 'light'
                    ? '0 4px 14px rgba(0, 168, 107, 0.4)'
                    : '0 4px 14px rgba(0, 230, 118, 0.4)',
                '&:hover': {
                  background: (theme) =>
                    theme.palette.mode === 'light'
                      ? `linear-gradient(135deg, ${theme.palette.secondary.dark} 0%, ${theme.palette.secondary.main} 100%)`
                      : `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.light} 100%)`,
                  boxShadow: (theme) =>
                    theme.palette.mode === 'light'
                      ? '0 6px 20px rgba(0, 168, 107, 0.6)'
                      : '0 6px 20px rgba(0, 230, 118, 0.6)'
                },
                '&.Mui-disabled': {
                  background: (theme) =>
                    theme.palette.mode === 'light'
                      ? 'rgba(0, 0, 0, 0.12)'
                      : 'rgba(255, 255, 255, 0.12)',
                  color: (theme) =>
                    theme.palette.mode === 'light'
                      ? 'rgba(0, 0, 0, 0.26)'
                      : 'rgba(255, 255, 255, 0.3)',
                  boxShadow: 'none'
                }
              }}
              disabled={isDownloadState && window.fileDetailsForDownload &&
                (window.fileDetailsForDownload.status === 'Not Available' ||
                 window.fileDetailsForDownload.status === 'Under Processing')}
            >
              Download Excel
            </Button>
          </Box>
        </Box>
      )}

      {!isFileUploaded && !isDownloadState && (
        <Grid container justifyContent="flex-end">
          <Button
            variant="contained"
            color="primary"
            startIcon={<UploadFile sx={{ color: '#ffffff' }} />}
            onClick={handleUpload}
            size="large"
            disabled={!selectedFile || isProcessing}
            sx={{
              borderRadius: 4,
              px: 4,
              py: 1.2,
              color: '#ffffff',
              fontWeight: 600,
              background: (theme) =>
                theme.palette.mode === 'light'
                  ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
                  : `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
              boxShadow: (theme) =>
                theme.palette.mode === 'light'
                  ? '0 4px 14px rgba(10, 75, 148, 0.3)'
                  : '0 4px 14px rgba(33, 150, 243, 0.3)',
              '&:hover': {
                background: (theme) =>
                  theme.palette.mode === 'light'
                    ? `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`
                    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
                boxShadow: (theme) =>
                  theme.palette.mode === 'light'
                    ? '0 6px 20px rgba(10, 75, 148, 0.4)'
                    : '0 6px 20px rgba(33, 150, 243, 0.4)',
              },
              '&.Mui-disabled': {
                background: (theme) =>
                  theme.palette.mode === 'light'
                    ? 'rgba(0, 0, 0, 0.12)'
                    : 'rgba(255, 255, 255, 0.12)',
                color: (theme) =>
                  theme.palette.mode === 'light'
                    ? 'rgba(0, 0, 0, 0.26)'
                    : 'rgba(255, 255, 255, 0.3)',
              }
            }}
          >
            Upload & Process
          </Button>
        </Grid>
      )}
    </Paper>
  );
});

export default FileUploader;
