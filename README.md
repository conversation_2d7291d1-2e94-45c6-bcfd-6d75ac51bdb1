# US Bank Statement Processor

This application processes US Bank statements in PDF format and generates Excel reports with the extracted data.

## Features

- Upload and process US Bank statements in PDF format
- Extract data using AWS Textract and OpenAI
- Generate Excel reports with the extracted data
- Download the Excel reports directly from the web interface
- Handle multi-page PDFs by processing each page individually and merging the results

## Project Structure

- `src/` - Backend Python code
  - `bankStatement.py` - Main bank statement processing logic
  - `excel.py` - Excel report generation
  - `main.py` - Command-line interface
- `US-Bank-Statement/us-bank-gl-processor/` - Frontend React application
- `api_server.py` - Flask API server that connects the frontend to the backend

## Setup and Installation

### Prerequisites

- Python 3.8 or higher
- Node.js 14 or higher
- AWS account with Textract access
- OpenAI API key

### Backend Setup

1. Install the required Python packages:

```bash
pip install -r requirements.txt
```

2. Configure your AWS credentials:

```bash
aws configure
```

3. Set your OpenAI API key as an environment variable:

```bash
# On Windows
set OPENAI_API_KEY=your-api-key

# On macOS/Linux
export OPENAI_API_KEY=your-api-key
```

### Frontend Setup

1. Navigate to the frontend directory:

```bash
cd US-Bank-Statement/us-bank-gl-processor
```

2. Install the required Node.js packages:

```bash
npm install
```

## Running the Application

### Start the API Server

```bash
python api_server.py
```

This will start the Flask API server on http://************:5000.

### Start the Frontend Development Server

```bash
cd US-Bank-Statement/us-bank-gl-processor
npm run dev -- --host # For network access (LAN)
```

This will start the frontend development server on http://************:5173.

### Using the Application

1. Open your web browser and navigate to http://************:5173
2. Upload a US Bank statement PDF file
3. Wait for the processing to complete
4. The Excel report will be automatically downloaded

## Command-Line Usage

You can also process bank statements from the command line:

```bash
python src/main.py path/to/your/statement.pdf
```

## Troubleshooting

### API Server Issues

- Check that the Flask API server is running on http://************:5000
- Check the logs in the `logs/log` file for error messages

### Frontend Issues

- Check the browser console for error messages
- Make sure the API server URL in `FileUploader.tsx` matches the actual API server URL

### Processing Issues

- Check that your AWS credentials are correctly configured
- Check that your OpenAI API key is correctly set
- Check the logs in the `logs/log` file for error messages

## License

This project is licensed under the MIT License - see the LICENSE file for details.
