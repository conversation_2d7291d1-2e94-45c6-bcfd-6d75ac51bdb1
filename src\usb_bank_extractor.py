#!/usr/bin/env python3
"""
USB Bank Detail Extraction - Consolidated Process

This script provides a complete solution for extracting details from bank statements.
It handles PDF processing, text extraction using AWS Textract, and data extraction using OpenAI.

The process includes:
1. PDF to text conversion using AWS Textract
2. Data extraction using OpenAI API
3. Result storage and file management
"""

import asyncio
import aioboto3
import aiofiles
import json
import sys
import logging
import os
import traceback
import shutil
import uuid
from botocore.exceptions import ClientError
from openai import OpenAI
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, Union, List, Optional
from pathlib import Path
from io import StringIO

# Constants
PROCESSED_FOLDER_NAME = "processed"
GPT_RESPONSE_SUFFIX = "_gptResponse"
TEXT_CONVERSION_SUFFIX = "_strUserPrompt"

# Configure logging
def setup_logging():
    """Set up logging to both console and file."""
    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Use a single log file named "log"
    log_file = os.path.join(logs_dir, "log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers to prevent duplicate logs
    if logger.handlers:
        logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create file handler with append mode
    file_handler = logging.FileHandler(log_file, mode='a')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Set up logging
logger = setup_logging()

class Config:
    """Configuration manager for the application."""

    @staticmethod
    def load_config(config_path: str = 'config/config.json') -> Dict[str, Any]:
        """
        Load configuration settings from a JSON file.

        Args:
            config_path: Path to the configuration JSON file

        Returns:
            Dictionary containing configuration settings or empty dict on error
        """
        try:
            with open(config_path, 'r') as file:
                return json.load(file)
        except Exception as e:
            logging.error(f"Failed to load config file {config_path}: {e}")
            return {}

    @staticmethod
    def load_system_prompt(folder_path: str, file_name: str) -> str:
        """
        Load the system prompt from a text file.

        Args:
            folder_path: Path to the folder containing the system prompt file
            file_name: Name of the system prompt text file

        Returns:
            Content of the system prompt file as a string or empty string on error
        """
        file_path = os.path.join(folder_path, file_name)
        try:
            with open(file_path, 'r') as file:
                return file.read()
        except Exception as e:
            logging.error(f"Failed to load system prompt file {file_path}: {e}")
            return ""

    @staticmethod
    def load_response_format(folder_path: str, file_name: str) -> Dict[str, Any]:
        """
        Load the response format from a JSON file.

        Args:
            folder_path: Path to the folder containing response format file
            file_name: Name of the response format JSON file

        Returns:
            Dictionary containing the response format or empty dict on error
        """
        file_path = os.path.join(folder_path, file_name)
        try:
            with open(file_path, 'r') as file:
                return json.load(file)
        except Exception as e:
            logging.error(f"Failed to load response format file {file_path}: {e}")
            return {}


class AWSTextract:
    """Handles AWS Textract operations for PDF text extraction."""

    @staticmethod
    async def upload_to_s3(s3_client, local_file_path: str, bucket_name: str, object_name: Optional[str] = None) -> str:
        """
        Upload a file to an S3 bucket.

        Args:
            s3_client: Boto3 S3 client
            local_file_path: Path to the file to upload
            bucket_name: S3 bucket name
            object_name: S3 object name (if None, uses basename of local_file_path)

        Returns:
            S3 object key of the uploaded file
        """
        if object_name is None:
            object_name = f"{uuid.uuid4().hex}_{os.path.basename(local_file_path)}"

        try:
            await s3_client.upload_file(local_file_path, bucket_name, object_name)
            logging.info(f"Uploaded {local_file_path} to {bucket_name}/{object_name}")
            return object_name
        except ClientError as e:
            logging.error(f"Error uploading to S3: {e}")
            sys.exit(1)

    @staticmethod
    async def start_document_text_detection(textract_client, bucket_name: str, object_name: str,
                                           job_tag: str, notification_channel: Optional[Dict] = None) -> str:
        """
        Start an asynchronous text detection job with AWS Textract.

        Args:
            textract_client: Boto3 Textract client
            bucket_name: S3 bucket name
            object_name: S3 object key
            job_tag: Identifier for the job
            notification_channel: Optional SNS notification configuration

        Returns:
            Job ID for the started text detection job
        """
        try:
            kwargs = {
                'DocumentLocation': {
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_name
                    }
                },
                'JobTag': job_tag
            }

            if notification_channel:
                kwargs['NotificationChannel'] = notification_channel

            response = await textract_client.start_document_text_detection(**kwargs)
            return response['JobId']
        except ClientError as e:
            logging.error(f"Error starting text detection job: {e}")
            sys.exit(1)

    @staticmethod
    async def get_document_text_detection(textract_client, job_id: str) -> List[Dict]:
        """
        Get results from a text detection job.

        Args:
            textract_client: Boto3 Textract client
            job_id: Job ID from start_document_text_detection

        Returns:
            List of block objects containing detected text
        """
        blocks = []
        pagination_token = None

        try:
            while True:
                kwargs = {'JobId': job_id}
                if pagination_token:
                    kwargs['NextToken'] = pagination_token

                response = await textract_client.get_document_text_detection(**kwargs)

                if response['JobStatus'] == 'SUCCEEDED':
                    blocks.extend(response['Blocks'])

                    if 'NextToken' in response:
                        pagination_token = response['NextToken']
                    else:
                        break
                elif response['JobStatus'] == 'FAILED':
                    logging.error("Text detection job failed")
                    sys.exit(1)
                else:
                    await asyncio.sleep(5)  # Wait before checking again

            return blocks
        except ClientError as e:
            logging.error(f"Error getting text detection results: {e}")
            sys.exit(1)

    @staticmethod
    def get_text_in_order(blocks: List[Dict]) -> List[Dict]:
        """
        Process Textract blocks to extract text in reading order.

        Args:
            blocks: List of block objects from Textract

        Returns:
            Ordered list of text items with page number and coordinates
        """
        text_items = []

        for block in blocks:
            if block['BlockType'] == 'LINE':
                text_items.append({
                    'Page': block.get('Page', 1),
                    'Text': block.get('Text', ''),
                    'X1': block.get('Geometry', {}).get('BoundingBox', {}).get('Left', 0),
                    'Y1': block.get('Geometry', {}).get('BoundingBox', {}).get('Top', 0),
                    'X2': block.get('Geometry', {}).get('BoundingBox', {}).get('Left', 0) +
                          block.get('Geometry', {}).get('BoundingBox', {}).get('Width', 0),
                    'Y2': block.get('Geometry', {}).get('BoundingBox', {}).get('Top', 0) +
                          block.get('Geometry', {}).get('BoundingBox', {}).get('Height', 0)
                })

        # Sort by page, then by Y coordinate (top to bottom), then by X coordinate (left to right)
        return sorted(text_items, key=lambda x: (x['Page'], x['Y1'], x['X1']))


class OpenAIExtractor:
    """Handles data extraction using OpenAI API."""

    def __init__(self, file_path: str, vendor_name: str) -> None:
        """
        Initialize the OpenAI extractor.

        Args:
            file_path: Path to the PDF file to process
            vendor_name: Name of the vendor/extraction type (e.g., "creditInfo")
        """
        self.config = Config.load_config()
        self.client = OpenAI()

        self.file_path = file_path
        self.folder_path = os.path.dirname(file_path)
        self.file_name = os.path.basename(file_path)
        self.file_name_without_ext = os.path.splitext(self.file_name)[0]
        self.vendor_name = vendor_name

        # Dynamically select the system prompt based on the extraction type
        if vendor_name == "creditInfo":
            self.system_prompt_path = "resource/CreditSystemPrompt_V1.txt"
        elif vendor_name == "debitInfo":
            self.system_prompt_path = "resource/DebitSystemPrompt_V1.txt"
        elif vendor_name == "checkNumberInorder":
            self.system_prompt_path = "resource/checkNumberInorderSysPrompt.txt"
        elif vendor_name == "dailyEndingBalance":
            self.system_prompt_path = "resource/dailyEndingBalanceSysPrompt.txt"
        elif vendor_name == "OpeningPageInfo":
            self.system_prompt_path = "resource/OpeningPageSystemPromptV1.txt"
        else:
            # Fallback to the config value
            self.system_prompt_path = self.config.get("systemPromptFilePath")

        self.response_format_path = self.config.get("responseFormatFilePath")
        self.api_response_path = self.config.get("apiResponsesPath")

    async def load_configurations(self) -> None:
        """
        Load necessary configurations and prepare data for API call.
        """
        text_file_path = os.path.join(
            self.folder_path,
            self.file_name_without_ext,
            f"{self.file_name_without_ext}{TEXT_CONVERSION_SUFFIX}.txt"
        )

        # Extract text with AWS Textract if not present
        if not os.path.exists(text_file_path):
            await self.extract_text_from_pdf()

        # Load the user prompt
        with open(text_file_path, 'r') as file:
            self.user_content = file.read()

        # Load the system prompt
        try:
            with open(self.system_prompt_path, 'r') as file:
                self.system_prompt = file.read()
        except Exception as e:
            logging.error(f"Error loading system prompt: {e}")
            self.system_prompt = "Extract credit information from the bank statement."

        # Load the response format
        with open(self.response_format_path, 'r') as file:
            response_formats = json.load(file)

        self.response_format = response_formats[self.vendor_name]

    async def extract_text_from_pdf(self) -> None:
        """
        Extract text from PDF using AWS Textract.
        """
        await extract_by_aws_textract(self.file_path)

    def call_openai_api(self) -> Any:
        """
        Call OpenAI API to extract structured data.

        Returns:
            OpenAI API response object
        """
        try:
            logging.info("------------- Started extraction by OpenAI ------------------")

            # For json_schema type, we need to use a different approach
            if isinstance(self.response_format, dict) and "format" in self.response_format:
                if self.response_format["format"].get("type") == "json_schema":
                    # Use the chat.completions.create method with json_schema
                    schema = self.response_format["format"]["schema"]
                    # Add JSON instruction to the system prompt
                    system_prompt_with_json = self.system_prompt + "\nPlease provide your response in JSON format."

                    response = self.client.chat.completions.create(
                        model="gpt-4o-2024-08-06",
                        messages=[
                            {"role": "system", "content": system_prompt_with_json},
                            {"role": "user", "content": str(self.user_content)}
                        ],
                        response_format={"type": "json_object"},
                        max_tokens=16384,
                        seed=33,
                        temperature=0
                    )
                else:
                    # For regular JSON responses
                    # Add JSON instruction to the system prompt
                    system_prompt_with_json = self.system_prompt + "\nPlease provide your response in JSON format."

                    response = self.client.chat.completions.create(
                        model="gpt-4o-2024-08-06",
                        messages=[
                            {"role": "system", "content": system_prompt_with_json},
                            {"role": "user", "content": str(self.user_content)}
                        ],
                        response_format={"type": "json_object"},
                        max_tokens=16384,
                        seed=33,
                        temperature=0
                    )
            else:
                # Default to JSON response format
                # Add JSON instruction to the system prompt
                system_prompt_with_json = self.system_prompt + "\nPlease provide your response in JSON format."

                response = self.client.chat.completions.create(
                    model="gpt-4o-2024-08-06",
                    messages=[
                        {"role": "system", "content": system_prompt_with_json},
                        {"role": "user", "content": str(self.user_content)}
                    ],
                    response_format={"type": "json_object"},
                    max_tokens=16384,
                    seed=33,
                    temperature=0
                )

            logging.info("------------- OpenAI Extraction completed ------------------")
            self.response = response
            return response
        except Exception as e:
            logging.error(f"Exception occurred during OpenAI API call: {e}")
            return None

    def save_response(self) -> None:
        """
        Save the OpenAI API response to a JSON file and the parsed content to TrueData folder.
        """
        if not hasattr(self, 'response') or self.response is None:
            logging.error("No response to save")
            return

        # Create directory if it doesn't exist
        vendor_response_path = os.path.join(self.api_response_path, self.vendor_name)
        os.makedirs(vendor_response_path, exist_ok=True)

        # Save response to JSON file
        response_file_path = os.path.join(
            vendor_response_path,
            f"{self.file_name_without_ext}{GPT_RESPONSE_SUFFIX}.json"
        )

        try:
            # Extract the content from the ChatCompletion object
            response_dict = {}
            parsed_content = None

            if hasattr(self.response, 'model'):
                response_dict['model'] = self.response.model

            if hasattr(self.response, 'choices') and len(self.response.choices) > 0:
                if hasattr(self.response.choices[0], 'message') and hasattr(self.response.choices[0].message, 'content'):
                    response_dict['content'] = self.response.choices[0].message.content

                    # Try to parse the content as JSON if it's a string
                    try:
                        if isinstance(response_dict['content'], str):
                            parsed_content = json.loads(response_dict['content'])
                            response_dict['parsed_content'] = parsed_content
                    except json.JSONDecodeError:
                        # If it's not valid JSON, keep it as a string
                        pass

            # Save the extracted data
            with open(response_file_path, 'w', encoding='utf-8') as file:
                json.dump(response_dict, file, ensure_ascii=False, indent=4)

            logging.info(f"Response saved to {response_file_path}")

            # Save parsed content to TrueData folder
            if parsed_content:
                # Create TrueData folder structure if it doesn't exist
                true_data_base_path = "data/TrueData"
                os.makedirs(true_data_base_path, exist_ok=True)

                # Create subfolder for extraction type if it doesn't exist
                for folder in ["creditInfo", "debitInfo", "checkNumberInorder", "dailyEndingBalance", "OpeningPageInfo"]:
                    os.makedirs(os.path.join(true_data_base_path, folder), exist_ok=True)

                # Save parsed content to the corresponding subfolder
                true_data_path = os.path.join(true_data_base_path, self.vendor_name)
                true_data_file_path = os.path.join(
                    true_data_path,
                    f"{self.file_name_without_ext}_trueData.json"
                )

                with open(true_data_file_path, 'w', encoding='utf-8') as file:
                    json.dump(parsed_content, file, ensure_ascii=False, indent=4)

                logging.info(f"Parsed content saved to {true_data_file_path}")
        except Exception as e:
            logging.error(f"Error saving response: {e}")

    def move_file(self) -> None:
        """
        Move processed files to the processed folder.
        """
        # Create processed folder if it doesn't exist
        processed_path = os.path.join(self.folder_path, PROCESSED_FOLDER_NAME)
        os.makedirs(processed_path, exist_ok=True)

        try:
            # Move PDF file
            pdf_dest = os.path.join(processed_path, self.file_name)
            if os.path.exists(pdf_dest):
                os.remove(pdf_dest)  # Remove existing file if it exists
            shutil.move(self.file_path, processed_path)

            # Move folder containing extracted text
            source_folder = os.path.join(self.folder_path, self.file_name_without_ext)
            dest_folder = os.path.join(processed_path, self.file_name_without_ext)

            if os.path.exists(source_folder):
                if os.path.exists(dest_folder):
                    # If destination exists, remove it first
                    shutil.rmtree(dest_folder)
                shutil.move(source_folder, processed_path)

            logging.info(f"Moved files to {processed_path}")
        except Exception as e:
            logging.error(f"Error moving files: {e}")


async def extract_by_aws_textract(local_pdf_path: str) -> Dict:
    """
    Extract text from a PDF file using AWS Textract.

    Args:
        local_pdf_path: Path to the local PDF file

    Returns:
        Dictionary containing extracted text data
    """
    # Configuration
    bucket_name = 'testingparagtraders'  # Replace with your S3 bucket name
    object_name = None  # Optionally specify the S3 object name
    job_tag = "ExtractTextJob"
    notification_channel = None  # Replace with your SNS topic ARN if needed

    # Derive base filename from the input PDF file name
    base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]

    # Create output folder
    output_folder = os.path.join(os.path.dirname(local_pdf_path), base_filename)
    os.makedirs(output_folder, exist_ok=True)

    # Initialize AWS clients
    session = aioboto3.Session()
    async with session.client('s3') as s3_client, session.client('textract') as textract_client:
        # Upload PDF to S3
        logging.info(f"Uploading {local_pdf_path} to S3...")
        s3_key = await AWSTextract.upload_to_s3(s3_client, local_pdf_path, bucket_name, object_name)

        # Start text detection
        logging.info("Starting text detection with Textract...")
        job_id = await AWSTextract.start_document_text_detection(
            textract_client, bucket_name, s3_key, job_tag, notification_channel
        )

        # Get text detection results
        logging.info("Polling for text detection results...")
        blocks = await AWSTextract.get_document_text_detection(textract_client, job_id)

        # Extract text in reading order
        logging.info("Extracting text in reading order...")
        ordered_text = AWSTextract.get_text_in_order(blocks)

        # Save extracted text to file
        text_json_filename = os.path.join(
            output_folder, f"{base_filename}{TEXT_CONVERSION_SUFFIX}.txt"
        )

        try:
            # Format the text in CSV-like format
            csv_content = "Page No., Text, X1, Y1, X2, Y2\n"
            for item in ordered_text:
                csv_content += f"{item['Page']}, {item['Text']}, {item['X1']}, {item['Y1']}, {item['X2']}, {item['Y2']}\n"

            async with aiofiles.open(text_json_filename, 'w', encoding='utf-8') as f:
                await f.write(csv_content)

            logging.info(f"Extracted text saved to {text_json_filename}.")
            return {"status": "success", "file_path": text_json_filename}
        except Exception as e:
            logging.error(f"Error writing to file: {e}")
            sys.exit(1)


class DocumentProcessor:
    """Handles the processing of documents."""

    def __init__(self, vendor_name: str = "creditInfo") -> None:
        """
        Initialize the document processor.

        Args:
            vendor_name: Name of the vendor/extraction type (e.g., "creditInfo")
        """
        self.config = Config.load_config()
        self.vendor_name = vendor_name

    async def process_document(self, file_path: str) -> None:
        """
        Process a single document.

        Args:
            file_path: Path to the PDF file to process
        """
        logging.info(f"Processing {file_path}")

        extractor = OpenAIExtractor(file_path, self.vendor_name)
        await extractor.load_configurations()
        extractor.call_openai_api()
        extractor.save_response()
        extractor.move_file()

        logging.info(f"Completed processing {file_path}")

    async def process_all_documents(self, files_to_process: int = 1) -> None:
        """
        Process multiple documents.

        Args:
            files_to_process: Maximum number of files to process
        """
        folder_path = os.path.join(self.config.get("inputDataPath", "data/inputData"), self.vendor_name)
        logging.info(f"Processing documents in {folder_path}")

        files_processed = 0

        try:
            for file in os.listdir(folder_path):
                if file.lower().endswith(".pdf"):
                    file_path = os.path.join(folder_path, file)
                    await self.process_document(file_path)

                    files_processed += 1
                    if files_processed >= files_to_process:
                        break
        except Exception as e:
            logging.error(f"Error processing documents: {e}")
            traceback.print_exc()


async def main():
    """Main entry point for the application."""
    # Configuration variables - modify these as needed
    extractionType = "OpeningPageInfo"  # Extraction type (e.g., creditInfo, debitInfo, checkNumberInorder, dailyEndingBalance, OpeningPageInfo)
    files_to_process = 1   # Number of files to process

    # Set to None to process files from the extractionType directory instead of a specific file
    single_file = r"OpeningPage.pdf"     # Path to a single file to process (None to process multiple files)

    # If you want to process a specific file, uncomment and modify the line below:
    # single_file = r"path/to/your/file.pdf"

    processor = DocumentProcessor(extractionType)

    if single_file and os.path.exists(single_file):
        await processor.process_document(single_file)
    else:
        await processor.process_all_documents(files_to_process)


if __name__ == "__main__":
    asyncio.run(main())
