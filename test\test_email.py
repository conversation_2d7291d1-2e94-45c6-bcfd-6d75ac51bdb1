#!/usr/bin/env python3
"""
Test Email Functionality

This script tests the email notification functionality by sending a test email.
"""

import json
import os
import sys
import logging
import smtplib
import argparse
from email.message import EmailMessage

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('email-test')

def send_test_email(debug=False, alt_port=None, use_ssl=False):
    """Send a test email using the configuration in config.json."""
    # Determine the path to the config file
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, 'config.json')

    # Load configuration
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        logger.info(f"Loaded configuration from {config_path}")
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.error(f"Failed to load configuration: {e}")
        return False

    # Check if email notifications are enabled
    if not config.get('email', {}).get('enabled', False):
        logger.info("Email notifications are disabled")
        return False

    try:
        # Get email configuration
        email_config = config.get('email', {})
        smtp_server = email_config.get('smtp_server')
        smtp_port = email_config.get('smtp_port')
        username = email_config.get('username')
        password = email_config.get('password')
        sender = email_config.get('sender')
        subject_prefix = email_config.get('subject_prefix', '[Service Health Check]')

        # Handle both old and new recipient formats
        recipients_config = email_config.get('recipients', {})
        if isinstance(recipients_config, dict):
            # New format with separate lists for success and failure
            # For test email, use both success and failure recipients
            success_recipients = recipients_config.get('success', [])
            failure_recipients = recipients_config.get('failure', [])
            # Combine both lists and remove duplicates
            recipients = list(set(success_recipients + failure_recipients))
            logger.info("Using combined recipients list from success and failure")
        else:
            # Fallback to old format with a single list
            recipients = recipients_config
            logger.info("Using common recipients list")

        if not all([smtp_server, smtp_port, username, password, sender]) or not recipients:
            logger.error("Incomplete email configuration or empty recipient list")
            return False

        # Create message using EmailMessage (like your working code)
        msg = EmailMessage()
        msg['From'] = sender
        msg['To'] = ', '.join(recipients)
        msg['Subject'] = f"{subject_prefix} TEST EMAIL"

        # Build email body
        plain_body = """This is a test email to verify that the email notification functionality is working correctly.

If you received this email, the configuration is correct.

Configuration details:
- SMTP Server: {smtp_server}
- SMTP Port: {smtp_port}
- Sender: {sender}
- Recipients: {recipients}
""".format(
            smtp_server=smtp_server,
            smtp_port=smtp_port,
            sender=sender,
            recipients=', '.join(recipients)
        )

        html_body = """<html>
<body>
<h2>Test Email - Zoho SMTP Configuration</h2>
<p>This is a test email to verify that the email notification functionality is working correctly.</p>
<p>If you received this email, the configuration is correct.</p>
<h3>Configuration details:</h3>
<ul>
<li><b>SMTP Server:</b> {smtp_server}</li>
<li><b>SMTP Port:</b> {smtp_port}</li>
<li><b>Sender:</b> {sender}</li>
<li><b>Recipients:</b> {recipients}</li>
</ul>
</body>
</html>
""".format(
            smtp_server=smtp_server,
            smtp_port=smtp_port,
            sender=sender,
            recipients=', '.join(recipients)
        )

        # Set content like your working code
        msg.set_content(plain_body)
        msg.add_alternative(html_body, subtype='html')

        # Use alternative port if specified
        if alt_port:
            smtp_port = alt_port
            logger.info(f"Using alternative port: {smtp_port}")

        # Connect to SMTP server and send email
        logger.info(f"Connecting to SMTP server {smtp_server}:{smtp_port}...")

        # Enable debug mode if requested
        if debug:
            logger.info("Debug mode enabled")
            smtplib.SMTP.debuglevel = 1

        # Choose connection method based on settings
        if use_ssl:
            logger.info("Using SSL connection")
            with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
                if debug:
                    server.set_debuglevel(1)
                logger.info(f"Logging in as {username}...")
                server.login(username, password)
                logger.info(f"Sending email to {', '.join(recipients)}...")
                server.send_message(msg)
        else:
            logger.info("Using TLS connection")
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                if debug:
                    server.set_debuglevel(1)
                logger.info("Starting TLS...")
                server.starttls()
                logger.info(f"Logging in as {username}...")
                server.login(username, password)
                logger.info(f"Sending email to {', '.join(recipients)}...")
                server.send_message(msg)

        logger.info(f"Email notification sent to {', '.join(recipients)}")
        return True
    except smtplib.SMTPAuthenticationError as e:
        logger.error(f"SMTP Authentication Failed: {e}")
        logger.info("Possible solutions:")
        logger.info("1. Verify your password is correct")
        logger.info("2. Generate an app-specific password in your Zoho account settings")
        logger.info("3. Check if 2FA is enabled and requires app password")
        logger.info("4. Ensure the sender email matches the username")
        return False
    except smtplib.SMTPException as e:
        logger.error(f"SMTP Error: {e}")
        return False
    except Exception as e:
        logger.error(f"Failed to send email notification: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == '__main__':
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Test Zoho email notification functionality')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode for SMTP connection')
    parser.add_argument('--port', type=int, help='Alternative SMTP port to use (default: use config file)')
    parser.add_argument('--ssl', action='store_true', help='Use SSL instead of TLS')
    parser.add_argument('--try-all', action='store_true', help='Try all common SMTP configurations')
    args = parser.parse_args()

    logger.info("Testing email notification functionality...")

    if args.try_all:
        # Try different common configurations
        logger.info("Trying all common SMTP configurations...")
        configs = [
            {'port': 587, 'ssl': False, 'desc': 'TLS on port 587 (standard)'},
            {'port': 465, 'ssl': True, 'desc': 'SSL on port 465 (alternative)'},
            {'port': 25, 'ssl': False, 'desc': 'TLS on port 25 (legacy)'}
        ]

        for config in configs:
            logger.info(f"\nTrying: {config['desc']}")
            if send_test_email(debug=args.debug, alt_port=config['port'], use_ssl=config['ssl']):
                logger.info(f"Email test successful with {config['desc']}!")
                sys.exit(0)
            logger.info(f"Failed with {config['desc']}")

        logger.error("All configurations failed!")
        sys.exit(1)
    else:
        # Try with specified or default configuration
        if send_test_email(debug=args.debug, alt_port=args.port, use_ssl=args.ssl):
            logger.info("Email test successful!")
            sys.exit(0)
        else:
            logger.error("Email test failed!")
            sys.exit(1)
