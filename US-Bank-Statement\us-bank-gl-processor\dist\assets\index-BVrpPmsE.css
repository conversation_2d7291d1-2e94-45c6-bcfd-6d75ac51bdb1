*{box-sizing:border-box;margin:0;padding:0}body{margin:0;font-family:Roboto,Helvetica,Arial,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;overflow-x:hidden;overscroll-behavior:none}#root{min-height:100vh;display:flex;flex-direction:column}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes slideUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}@keyframes shimmer{0%{background-position:-1000px 0}to{background-position:1000px 0}}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background:#f1f1f1}::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:4px}::-webkit-scrollbar-thumb:hover{background:#a1a1a1}@media (prefers-color-scheme: dark){::-webkit-scrollbar-track{background:#1e1e1e}::-webkit-scrollbar-thumb{background:#555}::-webkit-scrollbar-thumb:hover{background:#777}}.animate-pulse{animation:pulse 2s infinite ease-in-out}.animate-fade-in{animation:fadeIn .3s ease-in-out}.animate-slide-up{animation:slideUp .4s ease-out}.shimmer-effect{background:linear-gradient(90deg,#fff0,#fff3,#fff0);background-size:1000px 100%;animation:shimmer 2s infinite linear}.scrollable-container{overflow-y:auto;overflow-x:hidden;scrollbar-width:thin;overscroll-behavior:contain;-webkit-overflow-scrolling:touch}
