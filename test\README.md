# Service Health Check Tests

This directory contains tests to verify that the frontend and backend services are running correctly.

## Configuration

The tests use a configuration file (`config.json`) to determine the URLs to check and email notification settings. You can modify this file to match your environment:

```json
{
    "frontend": {
        "url": "http://************:5173",
        "timeout": 5
    },
    "backend": {
        "url": "http://************:5000",
        "api_endpoint": "/api/health",
        "timeout": 5
    },
    "email": {
        "enabled": true,
        "smtp_server": "smtp.zoho.com",
        "smtp_port": 587,
        "username": "<EMAIL>",
        "password": "your-zoho-password",
        "sender": "<EMAIL>",
        "recipients": ["<EMAIL>", "<EMAIL>"],
        "subject_prefix": "[Service Health Check]"
    }
}
```

### Service Configuration

- `frontend.url`: The URL of the frontend service
- `frontend.timeout`: Timeout in seconds for the frontend request
- `backend.url`: The URL of the backend service
- `backend.api_endpoint`: The API endpoint to check for the backend health
- `backend.timeout`: Timeout in seconds for the backend request

### Email Configuration

- `email.enabled`: Set to `true` to enable email notifications, `false` to disable
- `email.smtp_server`: SMTP server address (e.g., `smtp.zoho.com`)
- `email.smtp_port`: SMTP server port (typically 587 for TLS)
- `email.username`: Your Zoho email address
- `email.password`: Your Zoho email password or app-specific password
- `email.sender`: The email address to send from (usually the same as `username`)
- `email.recipients`: Can be configured in two ways:
  - Simple format: Array of email addresses to send notifications to for all test results
  - Advanced format: Object with separate recipient lists for success and failure:
    ```json
    "recipients": {
        "success": ["<EMAIL>"],
        "failure": ["<EMAIL>", "<EMAIL>"]
    }
    ```
- `email.subject_prefix`: Prefix for the email subject line

### Security Note

It's recommended to use environment variables or a secure vault for storing sensitive information like passwords in production environments. The current implementation stores the password in the config file for simplicity, but this should be improved for production use.

## Running the Tests Manually

To run the tests manually and send email notifications with the results, use the following command:

```bash
python test_service_health.py
```

This will run the tests and send an email notification with the results if email is enabled in the configuration.

If you want to run the tests without sending email notifications, you can use the standard unittest command:

```bash
python -m unittest test_service_health.py
```

## Jenkins Integration

The tests are designed to be run as part of a Jenkins CI/CD pipeline. The `Jenkinsfile` in the root directory contains the pipeline configuration.

To set up the Jenkins pipeline:

1. Create a new Jenkins pipeline job
2. Configure it to use the Git repository containing this code
3. Set the pipeline script path to `Jenkinsfile`
4. For secure email credentials, configure Jenkins credentials and update the Jenkinsfile to use them
5. Save and run the pipeline

Example Jenkins pipeline step with email notifications:

```groovy
stage('Service Health Check') {
    steps {
        withCredentials([string(credentialsId: 'zoho-email-password', variable: 'ZOHO_PASSWORD')]) {
            sh '''
                cd test
                # Update config with password from credentials
                jq --arg pwd "$ZOHO_PASSWORD" '.email.password = $pwd' config.json > config.json.tmp && mv config.json.tmp config.json
                python test_service_health.py
            '''
        }
    }
}

## Customizing the Tests

You can customize the tests by modifying the following files:

- `config.json`: Change the URLs, timeouts, and email settings
- `test_service_health.py`: Add additional tests or modify the existing ones
- `Jenkinsfile`: Customize the Jenkins pipeline configuration

### Customizing Email Notifications

#### Different Recipients for Success and Failure

The system supports sending notifications to different recipients based on the test result:

- **Success notifications**: When all tests pass, notifications are sent only to the recipients in the `success` list
- **Failure notifications**: When any test fails, notifications are sent to the recipients in the `failure` list

This allows you to:
- Send success notifications to a smaller group (e.g., just the development team)
- Send failure notifications to a larger group (e.g., development team + managers + operations)

#### Other Customizations

You can further customize the email notifications by modifying the `send_email_notification` method in `test_service_health.py`. For example, you can:

- Change the email format (HTML instead of plain text)
- Add attachments (e.g., log files)
- Customize the email subject and body
- Add more information to the email (e.g., detailed test results)

## Troubleshooting

If the tests fail, check the following:

1. Make sure the frontend and backend services are running
2. Verify that the URLs in `config.json` are correct
3. Check if the services are accessible from the Jenkins server
4. Ensure that the network allows connections to the specified ports

If email notifications are not being sent, check the following:

1. Verify that `email.enabled` is set to `true` in `config.json`
2. Check that the SMTP server and port are correct
3. Ensure that the username and password are valid
4. Check if the SMTP server requires additional security settings
5. Verify that the recipient email addresses are valid
6. Look for error messages in the logs related to email sending
