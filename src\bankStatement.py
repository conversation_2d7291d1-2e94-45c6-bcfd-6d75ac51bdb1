#!/usr/bin/env python3
"""
USB Bank Statement Processor

This script combines the functionality of pdfSeparator.py and usb_bank_extractor.py.
It separates a bank statement PDF into multiple PDFs and then processes each one
with the appropriate extraction type.

Workflow:
1. Take a PDF file as input
2. Separate it into multiple PDFs
3. Save the separated PDFs in Data1/[date_time]/PDFs/
4. Save the original PDF in Data1/[date_time]/inputPDF/
5. Process each separated PDF with the appropriate extraction type
6. Store all the extracted data in Data1/[date_time]/response/
"""

import os
import sys
import shutil
import asyncio
import json
import datetime
import boto3
import aioboto3
import aiofiles
import logging
import traceback
import uuid
from concurrent.futures import ThreadPoolExecutor
from botocore.exceptions import ClientError
from openai import OpenAI
from PyPDF2 import PdfReader, PdfWriter
from pdf2image import convert_from_path
import re
import tempfile
import time
import hashlib
import concurrent.futures
from pathlib import Path
from io import StringIO
from typing import Dict, Any, Union, List, Optional
import platform
import socket

TIME_STAMP = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

# Import colorama for cross-platform colored terminal output
try:
    import colorama
    from colorama import Fore, Style
    colorama.init()
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False

# Configure logging
def setup_logging():
    """Set up logging to both console and file."""
    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Use a single log file named "log"
    log_file = os.path.join(logs_dir, "log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers to prevent duplicate logs
    if logger.handlers:
        logger.handlers.clear()

    # Create formatter for file handler
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create file handler with append mode
    file_handler = logging.FileHandler(log_file, mode='a')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(file_formatter)

    # Create a custom formatter for colored console output
    if COLORAMA_AVAILABLE:
        class ColoredFormatter(logging.Formatter):
            """Custom formatter to add colors only to the log level name"""
            def __init__(self, fmt=None, datefmt=None, style='%'):
                super().__init__(fmt, datefmt, style)
                # Define colors for different log levels
                self.level_colors = {
                    'INFO': Fore.GREEN,
                    'WARNING': Fore.YELLOW,
                    'ERROR': Fore.RED
                }

            def format(self, record):
                # Save the original levelname
                original_levelname = record.levelname

                # Apply color only to the levelname if it has a defined color
                if record.levelname in self.level_colors:
                    record.levelname = f"{self.level_colors[record.levelname]}{record.levelname}{Style.RESET_ALL}"

                # Format the record with the colored levelname
                result = super().format(record)

                # Restore the original levelname for future use
                record.levelname = original_levelname

                return result

        # Create console formatter with colors
        console_formatter = ColoredFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    else:
        # Fallback to standard formatter if colorama is not available
        console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Set up logging
logger = setup_logging()
logger.info(f"Logging initialized. Log file: {os.path.join('logs', os.path.basename(logger.handlers[0].baseFilename))}")

# Constants
PROCESSED_FOLDER_NAME = "processed"
GPT_RESPONSE_SUFFIX = "_gptResponse"
TEXT_CONVERSION_SUFFIX = "_strUserPrompt"

def filter_text_by_tables(ordered_text: List[Dict], tables: List[List[List[str]]]) -> List[Dict]:
    """
    Filter out text items that appear in tables to avoid duplicates.

    Args:
        ordered_text: List of text items, each with 'Text', 'Page', and position info.
        tables: List of tables, each table is a list of rows, each row is a list of cell texts.

    Returns:
        Filtered list of text items that don't appear in tables.
    """
    if not tables:
        return ordered_text

    # Create a set of normalized table cell texts
    table_text_set = set()
    for table in tables:
        for row in table:
            for cell in row:
                # Normalize the cell text (lowercase, strip whitespace)
                norm_cell = cell.strip().lower()
                if norm_cell:
                    table_text_set.add(norm_cell)

    # Filter out text items that appear in tables
    filtered_text = []
    for item in ordered_text:
        text = item['Text']
        norm_text = text.strip().lower()

        # Check if this text appears in any table cell
        if norm_text not in table_text_set:
            # Also check for partial matches (text might be part of a table cell)
            is_in_table = False
            for table_text in table_text_set:
                if norm_text in table_text or table_text in norm_text:
                    # If there's significant overlap, consider it a match
                    if len(norm_text) > 3 and (len(norm_text) >= len(table_text) * 0.7 or len(table_text) >= len(norm_text) * 0.7):
                        is_in_table = True
                        break

            if not is_in_table:
                filtered_text.append(item)

    return filtered_text

# PDF Separator Functions
def get_cache_dir():
    """Get the cache directory for storing extracted text."""
    cache_dir = os.path.join(os.path.expanduser("~"), ".pdf_separator_cache")
    try:
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        elif not os.path.isdir(cache_dir):
            # If it exists but is not a directory, append a suffix
            base_cache_dir = cache_dir
            suffix = 1
            while os.path.exists(cache_dir) and not os.path.isdir(cache_dir):
                cache_dir = f"{base_cache_dir}_{suffix}"
                suffix += 1
            os.makedirs(cache_dir)
    except Exception as e:
        print(f"Warning: Could not create cache directory: {e}")
        # Use a temporary directory as fallback
        cache_dir = os.path.join(tempfile.gettempdir(), ".pdf_separator_cache")
        os.makedirs(cache_dir, exist_ok=True)

    return cache_dir

def get_cache_key(pdf_path, page_index):
    """Generate a unique cache key for a PDF page."""
    # Get file modification time to invalidate cache when file changes
    mtime = os.path.getmtime(pdf_path)
    # Get file size as additional check
    size = os.path.getsize(pdf_path)
    # Create a unique key based on file path, modification time, size, and page index
    key = f"{pdf_path}_{mtime}_{size}_{page_index}"
    # Hash the key to create a filename-safe string
    return hashlib.md5(key.encode()).hexdigest()

def get_cached_text(pdf_path, page_index):
    """Get cached text for a PDF page if available."""
    try:
        cache_key = get_cache_key(pdf_path, page_index)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, cache_key + ".json")

        if os.path.exists(cache_file) and os.path.isfile(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    data = json.load(f)
                    return data.get('text', '')
            except Exception as e:
                # If there's any error reading the cache, log it and ignore
                print(f"Warning: Could not read cache file {cache_file}: {e}")
                return None
    except Exception as e:
        print(f"Warning: Error accessing cache: {e}")

    return None

def save_text_to_cache(pdf_path, page_index, text):
    """Save extracted text to cache."""
    try:
        cache_key = get_cache_key(pdf_path, page_index)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, cache_key + ".json")

        # Ensure the directory exists
        os.makedirs(os.path.dirname(cache_file), exist_ok=True)

        with open(cache_file, 'w') as f:
            json.dump({'text': text}, f)
    except Exception as e:
        # If there's any error saving to cache, just log it and continue
        print(f"Warning: Could not save to cache: {e}")

def extract_text_with_textract(pdf_path, page_index, debug=False):
    """
    Extract text from a PDF page using AWS Textract with caching.
    """
    # Check cache first
    cached_text = get_cached_text(pdf_path, page_index)
    if cached_text is not None:
        if debug:
            print(f"Using cached text for page {page_index+1}")
        return cached_text

    try:
        # Convert the PDF page to an image
        if debug:
            print(f"Converting page {page_index+1} to image...")

        images = convert_from_path(pdf_path, first_page=page_index+1, last_page=page_index+1)
        if not images:
            print(f"Warning: Could not convert page {page_index+1} to image")
            return ""

        # Save the image to a temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp:
            temp_filename = temp.name
            images[0].save(temp_filename, 'PNG')

        try:
            # Call AWS Textract
            if debug:
                print(f"Calling AWS Textract for page {page_index+1}...")

            textract = boto3.client('textract')

            with open(temp_filename, 'rb') as document:
                response = textract.detect_document_text(
                    Document={'Bytes': document.read()}
                )

            # Extract text from the response
            text = ""
            for item in response['Blocks']:
                if item['BlockType'] == 'LINE':
                    text += item['Text'] + "\n"

            if debug:
                print(f"AWS Textract extracted {len(text)} characters")

            # Save to cache
            save_text_to_cache(pdf_path, page_index, text)

            return text

        finally:
            # Clean up the temporary file
            if os.path.exists(temp_filename):
                os.remove(temp_filename)

    except Exception as e:
        print(f"Error extracting text with AWS Textract: {e}")
        # Return empty string on error
        return ""

def extract_text_from_page(page_obj, pdf_path, page_index, debug=False):
    """
    Extract text from a PDF page using AWS Textract.
    Falls back to PyPDF2 if AWS Textract fails.
    """
    # Try AWS Textract first
    text = extract_text_with_textract(pdf_path, page_index, debug)

    # If AWS Textract failed or returned empty text, fall back to PyPDF2
    if not text:
        if debug:
            print("AWS Textract failed or returned empty text, falling back to PyPDF2...")
        text = page_obj.extract_text() or ""

        # Clean up the text - remove extra whitespace and normalize
        if text:
            text = re.sub(r'\s+', ' ', text).strip()

    return text

def check_for_bank_statement_headers(text):
    """
    Special function to check for bank statement headers that might have dashes or other formatting.
    Returns the matched header or None if no match is found.
    """
    if not text:
        return None

    # Common patterns in bank statements with various formatting
    patterns = [
        # Deposits section with dashes - handle cases with and without spaces
        r'[-_=]*\s*Deposits\s*and\s*Other\s*Credits\s*[-_=]*',
        r'[-_=]*\s*DepositsandOther\s*Credits\s*[-_=]*',
        # Checks section with dashes - handle cases with and without spaces
        r'[-_=]*\s*Checks\s*and\s*Other\s*Debits\s*[-_=]*',
        r'[-_=]*\s*ChecksandOther\s*Debits\s*[-_=]*',
        # Checks in number order
        r'[-_=]*\s*CHECKS\s*IN\s*NUMBER\s*ORDER\s*[-_=]*',
        r'[-_=]*\s*Checks\s*in\s*Number\s*Order\s*[-_=]*',
        # Previous balance
        r'[-_=]*\s*Previous\s*Balance\s*[-_=]*',
        # Daily ending balance
        r'[-_=]*\s*Daily\s*Ending\s*Balance\s*[-_=]*',
        r'[-_=]*\s*Ending\s*Balance\s*[-_=]*'
    ]

    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(0)

    return None

def contains_text(text, search_text):
    """Check if the text contains the search text (case insensitive)."""
    if not text:
        return False

    # Normalize both texts for better matching
    text_normalized = text.lower()
    search_normalized = search_text.lower()

    # Try direct substring match
    if search_normalized in text_normalized:
        return True

    # Try with relaxed spacing
    search_pattern = r'\s*'.join(re.escape(c) for c in search_normalized)
    if re.search(search_pattern, text_normalized):
        return True

    # Try ignoring dashes, underscores and other formatting characters
    clean_text = re.sub(r'[-_=]+', '', text_normalized)
    clean_search = re.sub(r'[-_=]+', '', search_normalized)
    if clean_search in clean_text:
        return True

    # Try with word boundaries - useful for finding phrases
    words = search_normalized.split()
    if len(words) > 1:
        # Check if all words are present in the text in the correct order
        word_pattern = r'\b' + r'.*?\b'.join(re.escape(word) for word in words) + r'\b'
        if re.search(word_pattern, text_normalized):
            return True

        # Try with words potentially run together (no spaces)
        # For example, "Deposits and Other" might appear as "DepositsandOther"
        # First, remove all spaces from the search text
        no_spaces_search = search_normalized.replace(" ", "")
        if no_spaces_search in text_normalized.replace(" ", ""):
            return True

        # Try with some words run together
        # For example, "Deposits andOther Credits" or "Depositsand Other Credits"
        for i in range(1, len(words)):
            # Join words i-1 and i
            joined_words = words.copy()
            joined_words[i-1] = joined_words[i-1] + joined_words[i]
            joined_words.pop(i)

            # Create a pattern with these joined words
            joined_pattern = r'\b' + r'.*?\b'.join(re.escape(word) for word in joined_words) + r'\b'
            if re.search(joined_pattern, text_normalized):
                return True

    return False

def process_page(args):
    """
    Process a single PDF page in parallel.
    """
    i, page, pdf_path, debug = args

    # Extract text from the page
    text = extract_text_from_page(page, pdf_path, i, debug)
    results = []

    # Track which writer indices have already been added to avoid duplicates
    added_writers = set()

    # # Print the extracted text for debugging
    # if debug:
    #     print(f"\n--- Page {i+1} Extracted Text (first 500 chars) ---")
    #     print(text[:500] if text else "No text extracted")
    #     print("-------------------------------------------")

    # First try the special bank statement header detection
    header_match = check_for_bank_statement_headers(text)
    if header_match:
        # Determine which category this header belongs to
        header_lower = header_match.lower()

        if "deposits" in header_lower and "credits" in header_lower and 1 not in added_writers:
            results.append((1, f"special header '{header_match}'"))
            added_writers.add(1)

        if "checks" in header_lower and "debits" in header_lower and 2 not in added_writers:
            results.append((2, f"special header '{header_match}'"))
            added_writers.add(2)

        if "checks" in header_lower and "number order" in header_lower and 3 not in added_writers:
            results.append((3, f"special header '{header_match}'"))
            added_writers.add(3)

        if "previous" in header_lower and "balance" in header_lower and 0 not in added_writers:
            results.append((0, f"special header '{header_match}'"))
            added_writers.add(0)

        if ("daily" in header_lower and "ending" in header_lower and "balance" in header_lower and 4 not in added_writers) or \
           ("ending" in header_lower and "balance" in header_lower and 4 not in added_writers):
            results.append((4, f"special header '{header_match}'"))
            added_writers.add(4)

    # Define search terms for matching
    main_terms = [
        "Previous Balance",
        "Deposits and Other Credits",
        "Checks and Other Debits",
        "CHECKS IN NUMBER ORDER",
        "Daily Ending Balance"
    ]

    # Alternative search terms (variations that might appear in the PDF)
    alt_search_terms = {
        "Previous Balance": ["Previous Balance", "PreviousBalance", "Prev Balance", "Prev. Balance", "Previous Bal", "Prev Bal"],
        "Deposits and Other Credits": [
            "Deposits and Other Credits",
            "Deposits & Other Credits",
            "---Deposits and Other Credits---",
            "-Deposits and Other Credits-",
            "--Deposits and Other Credits--",
            "Deposits and Other Credits-",
            "-Deposits and Other Credits",
            "Deposits andOther Credits"
        ],
        "Checks and Other Debits": [
            "Checks and Other Debits",
            "Checks & Other Debits",
            "---Checks and Other Debits---",
            "-Checks and Other Debits-",
            "--Checks and Other Debits--",
            "Checks and Other Debits-",
            "-Checks and Other Debits"
        ],
        "CHECKS IN NUMBER ORDER": ["CHECKS IN NUMBER ORDER", "Checks in Number Order", "Check Number Order"],
        "Daily Ending Balance": [
            "Daily Ending Balance",
            "Daily Balance",
            "---Daily Ending Balance---",
            "-Daily Ending Balance-",
            "--Daily Ending Balance--",
            "Daily Ending Balance-",
            "-Daily Ending Balance"
        ]
    }

    # Check each search term and its alternatives
    for term_index, main_term in enumerate(main_terms):
        # Skip if this writer has already been added
        if term_index in added_writers:
            continue

        # Try the main term first
        if contains_text(text, main_term):
            results.append((term_index, f"'{main_term}'"))
            added_writers.add(term_index)
            continue

        # Try alternative terms
        for alt_term in alt_search_terms[main_term]:
            if alt_term != main_term and contains_text(text, alt_term):
                results.append((term_index, f"alternative '{alt_term}'"))
                added_writers.add(term_index)
                break

    return i, page, results

def separate_pdf(input_pdf_path, output_dir, debug=True, max_workers=10):
    """
    Separate a PDF into multiple PDFs based on specific text content.

    Conditions:
    1. If page contains "Previous Balance" -> OpeningPage.pdf
    2. If page contains "Deposits and Other Credits" -> CreditPages.pdf
    3. If page contains "Checks and Other Debits" -> DebitPages.pdf
    4. If page contains "CHECKS IN NUMBER ORDER" -> CheckPages.pdf
    5. If page contains "Daily Ending Balance" -> EndingBalance.pdf

    Args:
        input_pdf_path: Path to the input PDF file
        output_dir: Directory to save the output PDFs
        debug: Whether to print debug information
        max_workers: Maximum number of parallel workers

    Returns:
        List of paths to the created PDF files
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    print(f"Created output directory: {output_dir}")

    # Initialize PDF writers for each condition
    writers = [
        PdfWriter(),  # opening_writer (0)
        PdfWriter(),  # credit_writer (1)
        PdfWriter(),  # debit_writer (2)
        PdfWriter(),  # check_writer (3)
        PdfWriter()   # ending_writer (4)
    ]

    writer_names = [
        "OpeningPage.pdf",
        "CreditPages.pdf",
        "DebitPages.pdf",
        "CheckPages.pdf",
        "EndingBalance.pdf"
    ]

    # Track which pages were processed
    processed_pages = set()

    # Track which pages have been added to each writer to avoid duplicates
    # This is a set of page indices for each writer
    added_pages = [set() for _ in writers]

    # List to store paths of created PDFs
    created_pdfs = []

    try:
        # Read the input PDF
        reader = PdfReader(input_pdf_path)
        total_pages = len(reader.pages)
        print(f"Processing PDF with {total_pages} pages...")

        # Prepare arguments for parallel processing
        page_args = [(i, page, input_pdf_path, debug) for i, page in enumerate(reader.pages)]

        # Process pages in parallel
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(process_page, args) for args in page_args]

            # Process results as they complete
            for future in concurrent.futures.as_completed(futures):
                i, page, results = future.result()

                if results:
                    for writer_index, reason in results:
                        # Only add the page if it hasn't been added to this writer yet
                        if i not in added_pages[writer_index]:
                            writers[writer_index].add_page(page)
                            added_pages[writer_index].add(i)
                            print(f"Page {i+1}: Contains {reason} -> {writer_names[writer_index]}")
                        processed_pages.add(i)
                else:
                    print(f"Page {i+1}: No matching conditions found")

        end_time = time.time()
        print(f"\nText extraction and processing completed in {end_time - start_time:.2f} seconds")

        # Save the PDFs if they have pages
        for i, (writer, name) in enumerate(zip(writers, writer_names)):
            if len(writer.pages) > 0:
                output_path = os.path.join(output_dir, name)
                with open(output_path, "wb") as output_file:
                    writer.write(output_file)
                print(f"Created {name} with {len(writer.pages)} pages")
                created_pdfs.append(output_path)

        # Summary
        print(f"\nSummary:")
        print(f"Total pages in original PDF: {total_pages}")
        print(f"Pages processed: {len(processed_pages)}")
        for i, (writer, name) in enumerate(zip(writers, writer_names)):
            print(f"Pages in {name}: {len(writer.pages)} (unique pages: {len(added_pages[i])})")

    except Exception as e:
        print(f"Error processing PDF: {e}")
        return []

    return created_pdfs

# USB Bank Extractor Classes
class Config:
    """Configuration manager for the application."""

    @staticmethod
    def load_config(config_path: str = 'config/config.json') -> Dict[str, Any]:
        """Load configuration settings from a JSON file."""
        try:
            with open(config_path, 'r') as file:
                return json.load(file)
        except Exception as e:
            logging.error(f"Failed to load config file {config_path}: {e}")
            return {}

    @staticmethod
    def load_system_prompt(folder_path: str, file_name: str) -> str:
        """Load the system prompt from a text file."""
        file_path = os.path.join(folder_path, file_name)
        try:
            with open(file_path, 'r') as file:
                return file.read()
        except Exception as e:
            logging.error(f"Failed to load system prompt file {file_path}: {e}")
            return ""

    @staticmethod
    def load_response_format(folder_path: str, file_name: str) -> Dict[str, Any]:
        """Load the response format from a JSON file."""
        file_path = os.path.join(folder_path, file_name)
        try:
            with open(file_path, 'r') as file:
                return json.load(file)
        except Exception as e:
            logging.error(f"Failed to load response format file {file_path}: {e}")
            return {}


class AWSTextract:
    """Handles AWS Textract operations for PDF text extraction."""

    @staticmethod
    async def upload_to_s3(s3_client, local_file_path: str, bucket_name: str, object_name: Optional[str] = None) -> str:
        """Upload a file to an S3 bucket."""
        if object_name is None:
            object_name = f"{uuid.uuid4().hex}_{os.path.basename(local_file_path)}"

        try:
            await s3_client.upload_file(local_file_path, bucket_name, object_name)
            logging.info(f"Uploaded {local_file_path} to {bucket_name}/{object_name}")
            return object_name
        except ClientError as e:
            logging.error(f"Error uploading to S3: {e}")
            sys.exit(1)

    @staticmethod
    async def start_document_text_detection(textract_client, bucket_name: str, object_name: str,
                                           job_tag: str, notification_channel: Optional[Dict] = None) -> str:
        """Start an asynchronous text detection job with AWS Textract."""
        try:
            kwargs = {
                'DocumentLocation': {
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_name
                    }
                },
                'JobTag': job_tag
            }

            if notification_channel:
                kwargs['NotificationChannel'] = notification_channel

            response = await textract_client.start_document_text_detection(**kwargs)
            return response['JobId']
        except ClientError as e:
            logging.error(f"Error starting text detection job: {e}")
            sys.exit(1)

    @staticmethod
    async def start_document_analysis(textract_client, bucket_name: str, object_name: str,
                                     job_tag: str, notification_channel: Optional[Dict] = None) -> str:
        """Start an asynchronous document analysis job with Textract for tables."""
        try:
            kwargs = {
                'DocumentLocation': {
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_name
                    }
                },
                'FeatureTypes': ['TABLES'],
                'JobTag': job_tag
            }

            if notification_channel:
                kwargs['NotificationChannel'] = notification_channel

            response = await textract_client.start_document_analysis(**kwargs)
            return response['JobId']
        except ClientError as e:
            logging.error(f"Error starting document analysis: {e}")
            sys.exit(1)

    @staticmethod
    async def get_document_text_detection(textract_client, job_id: str) -> List[Dict]:
        """Get results from a text detection job."""
        blocks = []
        pagination_token = None

        try:
            while True:
                kwargs = {'JobId': job_id}
                if pagination_token:
                    kwargs['NextToken'] = pagination_token

                response = await textract_client.get_document_text_detection(**kwargs)

                if response['JobStatus'] == 'SUCCEEDED':
                    blocks.extend(response['Blocks'])

                    if 'NextToken' in response:
                        pagination_token = response['NextToken']
                    else:
                        break
                elif response['JobStatus'] == 'FAILED':
                    logging.error("Text detection job failed")
                    sys.exit(1)
                else:
                    await asyncio.sleep(5)  # Wait before checking again

            return blocks
        except ClientError as e:
            logging.error(f"Error getting text detection results: {e}")
            sys.exit(1)

    @staticmethod
    async def get_document_analysis(textract_client, job_id: str) -> List[Dict]:
        """Get results from a document analysis job."""
        blocks = []
        pagination_token = None

        try:
            while True:
                kwargs = {'JobId': job_id}
                if pagination_token:
                    kwargs['NextToken'] = pagination_token

                response = await textract_client.get_document_analysis(**kwargs)

                if response['JobStatus'] == 'SUCCEEDED':
                    blocks.extend(response['Blocks'])

                    if 'NextToken' in response:
                        pagination_token = response['NextToken']
                    else:
                        break
                elif response['JobStatus'] == 'FAILED':
                    logging.error("Document analysis job failed")
                    sys.exit(1)
                else:
                    await asyncio.sleep(5)  # Wait before checking again

            return blocks
        except ClientError as e:
            logging.error(f"Error getting document analysis results: {e}")
            sys.exit(1)

    @staticmethod
    def get_text_in_order(blocks: List[Dict]) -> List[Dict]:
        """Process Textract blocks to extract text in reading order."""
        text_items = []

        for block in blocks:
            if block['BlockType'] == 'LINE':
                text_items.append({
                    'Page': block.get('Page', 1),
                    'Text': block.get('Text', ''),
                    'X1': block.get('Geometry', {}).get('BoundingBox', {}).get('Left', 0),
                    'Y1': block.get('Geometry', {}).get('BoundingBox', {}).get('Top', 0),
                    'X2': block.get('Geometry', {}).get('BoundingBox', {}).get('Left', 0) +
                          block.get('Geometry', {}).get('BoundingBox', {}).get('Width', 0),
                    'Y2': block.get('Geometry', {}).get('BoundingBox', {}).get('Top', 0) +
                          block.get('Geometry', {}).get('BoundingBox', {}).get('Height', 0)
                })

        # Sort by page, then by Y coordinate (top to bottom), then by X coordinate (left to right)
        return sorted(text_items, key=lambda x: (x['Page'], x['Y1'], x['X1']))

    @staticmethod
    def extract_tables_from_blocks(blocks: List[Dict]) -> List[List[List[str]]]:
        """
        Extracts tables from Textract response blocks.

        Args:
            blocks: List of blocks from Textract response.

        Returns:
            List of tables, each table is a list of rows, each row is a list of cell texts.
        """
        tables = []
        table_blocks = [block for block in blocks if block['BlockType'] == 'TABLE']

        for table in table_blocks:
            cell_blocks = []

            # Extract CELL blocks related to the current table
            relationships = table.get('Relationships', [])
            for rel in relationships:
                if rel['Type'] == 'CHILD':
                    for child_id in rel['Ids']:
                        cell = next((b for b in blocks if b['Id'] == child_id and b['BlockType'] == 'CELL'), None)
                        if cell:
                            cell_blocks.append(cell)

            if not cell_blocks:
                continue

            # Sort cells by row and column
            sorted_cells = sorted(cell_blocks, key=lambda x: (x.get('RowIndex', 0), x.get('ColumnIndex', 0)))

            # Determine the number of rows and columns
            max_row = max(cell.get('RowIndex', 0) for cell in sorted_cells)
            max_col = max(cell.get('ColumnIndex', 0) for cell in sorted_cells)

            # Initialize table structure
            table_data = [["" for _ in range(max_col)] for _ in range(max_row)]

            for cell in sorted_cells:
                row = cell.get('RowIndex', 0) - 1  # Zero-based index
                col = cell.get('ColumnIndex', 0) - 1  # Zero-based index
                text = AWSTextract.get_text_for_block(blocks, cell)
                if 0 <= row < max_row and 0 <= col < max_col:
                    table_data[row][col] = text
                else:
                    logging.warning(f"Cell position out of bounds. Row: {row+1}, Column: {col+1}")

            tables.append(table_data)

        return tables

    @staticmethod
    def get_text_for_block(blocks: List[Dict], cell_block: Dict) -> str:
        """
        Retrieves the text associated with a cell block.

        Args:
            blocks: List of all blocks.
            cell_block: The cell block for which text is to be retrieved.

        Returns:
            Concatenated text within the cell.
        """
        text = []
        relationships = cell_block.get('Relationships', [])
        for rel in relationships:
            if rel['Type'] == 'CHILD':
                for child_id in rel['Ids']:
                    child = next((b for b in blocks if b['Id'] == child_id), None)
                    if child:
                        if child['BlockType'] == 'WORD':
                            text.append(child.get('Text', ''))
                        elif child['BlockType'] == 'SELECTION_ELEMENT' and child.get('SelectionStatus') == 'SELECTED':
                            text.append("X")
        return ' '.join(text)


class OpenAIExtractor:
    """Handles data extraction using OpenAI API."""

    def __init__(self, file_path: str, extraction_type: str, clientName="XAI") -> None:
        """Initialize the OpenAI extractor."""
        self.config = Config.load_config()
        self.clientName = clientName
        if self.clientName=="OpenAI":
            self.client = OpenAI()
        elif self.clientName=="XAI":
            self.client = OpenAI(
                api_key="************************************************************************************",
                base_url="https://api.x.ai/v1",
                )
            

        self.file_path = file_path
        self.folder_path = os.path.dirname(file_path)
        self.file_name = os.path.basename(file_path)
        self.file_name_without_ext = os.path.splitext(self.file_name)[0]
        self.extraction_type = extraction_type

        # Dynamically select the system prompt based on the extraction type
        if extraction_type == "creditInfo":
            self.system_prompt_path = "resource/CreditSystemPrompt_V1.txt"
        elif extraction_type == "debitInfo":
            self.system_prompt_path = "resource/DebitSystemPrompt_V1.txt"
        elif extraction_type == "checkNumberInorder":
            self.system_prompt_path = "resource/checkNumberInorderSysPrompt.txt"
        elif extraction_type == "dailyEndingBalance":
            self.system_prompt_path = "resource/dailyEndingBalanceSysPrompt.txt"
        elif extraction_type == "OpeningPageInfo":
            self.system_prompt_path = "resource/OpeningPageSystemPromptV1.txt"
        else:
            # Fallback to the config value
            self.system_prompt_path = self.config.get("systemPromptFilePath")

        # Set the response format path
        self.response_format_path = (self.config.get("responseFormatFilePath", "resource/ResponseFormat_V1_grok.json"))
        self.api_response_path = f"Data1/{TIME_STAMP}/apiResponses"

    async def load_configurations(self) -> None:
        """Load necessary configurations and prepare data for API call."""
        text_file_path = os.path.join(
            self.folder_path,
            self.file_name_without_ext,
            f"{self.file_name_without_ext}{TEXT_CONVERSION_SUFFIX}.txt"
        )

        # Extract text with AWS Textract if not present
        if not os.path.exists(text_file_path):
            await self.extract_text_from_pdf()

        # Load the user prompt
        with open(text_file_path, 'r') as file:
            self.user_content = file.read()

        # Load the system prompt
        try:
            with open(self.system_prompt_path, 'r') as file:
                self.system_prompt = file.read()
        except Exception as e:
            logging.error(f"Error loading system prompt: {e}")
            self.system_prompt = f"Extract {self.extraction_type} information from the bank statement."

        # Load the response format
        try:
            with open(self.response_format_path, 'r') as file:
                response_formats = json.load(file)

            # Get the response format for the specific extraction type
            if self.extraction_type in response_formats:
                self.response_format = response_formats[self.extraction_type]
            else:
                logging.error(f"No response format found for extraction type: {self.extraction_type}")
                self.response_format = {"format": {"type": "json_object"}}
        except Exception as e:
            logging.error(f"Error loading response format: {e}")
            self.response_format = {"format": {"type": "json_object"}}

    async def extract_text_from_pdf(self) -> None:
        """Extract text from PDF using AWS Textract."""
        await extract_by_aws_textract(self.file_path)

    async def call_openai_api(self) -> Any:
        """Call OpenAI API to extract structured data asynchronously."""
        try:
            logging.info("------------- Started extraction by OpenAI ------------------")

            # Add JSON instruction to the system prompt
            system_prompt_with_json = self.system_prompt + "\nPlease provide your response in JSON format."

            # Define the synchronous function to run in a separate thread
            def make_api_call():
                # Use only the o4-mini model API format
                if self.clientName == "OpenAI": 
                    object = self.client.responses.create(
                        model="o4-mini",
                        input=[
                            {"role": "system", "content": system_prompt_with_json},
                            {"role": "user", "content": str(self.user_content)}
                        ],
                        text = self.response_format,
                        reasoning={"effort": "high"},
                        metadata = {
                            "Project": "US Bank Statement Processor",
                            "ResponsibleDeveloper": "Meetul Agrawal",
                            "Hostname": socket.gethostname(),
                            "ipAddress": socket.gethostbyname(socket.gethostname()),
                            "Platform": platform.system(),
                            "User": os.getlogin() if hasattr(os, 'getlogin') else "unknown",
                            "FunctionKey": self.extraction_type,
                            "ClientName": "Patosi"
                        }
                    )
                elif self.clientName == "XAI":
                    object = self.client.chat.completions.create(
                        model="grok-3-mini",
                        messages=[
                            {"role": "system", "content": system_prompt_with_json},
                            {"role": "user", "content": str(self.user_content)}
                        ],
                        response_format=self.response_format.get("format"),
                        seed=33,
                        temperature=0,
                        reasoning_effort="high"
                    )

                return object

            # Run the API call in a separate thread to avoid blocking the event loop
            try:
                # Try using asyncio.to_thread (Python 3.9+)
                import asyncio
                response = await asyncio.to_thread(make_api_call)
            except (ImportError, AttributeError):
                # Fallback for Python < 3.9
                from concurrent.futures import ThreadPoolExecutor
                with ThreadPoolExecutor() as executor:
                    response = await asyncio.get_event_loop().run_in_executor(executor, make_api_call)

            logging.info("------------- OpenAI Extraction completed ------------------")
            self.response = response
            return response
        except Exception as e:
            logging.error(f"Exception occurred during OpenAI API call: {e}")
            return None

    def save_response(self, response_dir: str = None) -> None:
        """
        Save the OpenAI API response to a JSON file and the parsed content to TrueData folder.

        Args:
            response_dir: Optional custom directory to save the parsed content
        """
        if not hasattr(self, 'response') or self.response is None:
            logging.error("No response to save")
            return

        # Create directory if it doesn't exist
        vendor_response_path = os.path.join(self.api_response_path, self.extraction_type)
        os.makedirs(vendor_response_path, exist_ok=True)

        # Save response to JSON file
        response_file_path = os.path.join(
            vendor_response_path,
            f"{self.file_name_without_ext}{GPT_RESPONSE_SUFFIX}.json"
        )

        try:
            # Extract the content from the response object
            response_dict = {}
            parsed_content = None

            # Handle o4-mini model response structure
            if hasattr(self.response, 'model'):
                response_dict['model'] = self.response.model

            # Extract content from the response based on its structure
            content_text = None

            # Try to extract from o4-mini response structure
            if hasattr(self.response, 'output') and len(self.response.output) > 0:
                for output_item in self.response.output:
                    if hasattr(output_item, 'content') and len(output_item.content) > 0:
                        for content_item in output_item.content:
                            if hasattr(content_item, 'text'):
                                content_text = content_item.text
                                break
                        if content_text:
                            break

            # Fallback to traditional structure if o4-mini structure not found
            if not content_text and hasattr(self.response, 'choices') and len(self.response.choices) > 0:
                if hasattr(self.response.choices[0], 'message') and hasattr(self.response.choices[0].message, 'content'):
                    content_text = self.response.choices[0].message.content

            # Store the content in the response dictionary
            if content_text:
                response_dict['content'] = content_text

                # Try to parse the content as JSON if it's a string
                try:
                    try:
                        if hasattr(self.response, 'usage'):
                            usage_data = self.response.usage.__dict__
                            response_dict['token_usage'] = {
                                'input_tokens': usage_data.get('input_tokens'),
                                'output_tokens': usage_data.get('output_tokens'),
                                'total_tokens': usage_data.get('total_tokens')
                            }
                        elif hasattr(self.response, 'usage_metadata'):
                            metadata_data = self.response.usage_metadata.__dict__
                            response_dict['token_usage'] = {
                                'prompt_tokens': metadata_data.get('prompt_tokens'),
                                'completion_tokens': metadata_data.get('completion_tokens'),
                                'total_tokens': metadata_data.get('total_tokens')
                            }
                    except Exception as ex:
                        logging.warning(f"Failed to extract token usage info: {ex}")
                    if isinstance(content_text, str):
                        # Clean up the content text - remove markdown code block markers if present
                        cleaned_text = content_text

                        # Remove ```json at the beginning if present
                        if cleaned_text.strip().startswith("```json"):
                            cleaned_text = cleaned_text.strip()[7:].strip()

                        # Remove ``` at the end if present
                        if cleaned_text.strip().endswith("```"):
                            cleaned_text = cleaned_text.strip()[:-3].strip()

                        # Try to parse the cleaned text
                        try:
                            parsed_content = json.loads(cleaned_text)
                            response_dict['parsed_content'] = parsed_content
                        except json.JSONDecodeError as e:
                            # If parsing fails, try to fix common issues
                            logging.warning(f"Initial JSON parsing failed: {str(e)}. Attempting to fix JSON...")

                            # Try to fix truncated JSON by adding missing closing brackets
                            fixed_text = cleaned_text

                            # Count opening and closing braces/brackets
                            open_braces = fixed_text.count('{')
                            close_braces = fixed_text.count('}')
                            open_brackets = fixed_text.count('[')
                            close_brackets = fixed_text.count(']')

                            # Add missing closing braces/brackets
                            if open_braces > close_braces:
                                fixed_text += '}' * (open_braces - close_braces)
                            if open_brackets > close_brackets:
                                fixed_text += ']' * (open_brackets - close_brackets)

                            # Try parsing again with the fixed text
                            try:
                                parsed_content = json.loads(fixed_text)
                                response_dict['parsed_content'] = parsed_content
                                logging.info("Successfully fixed and parsed JSON")
                            except json.JSONDecodeError as e2:
                                # If it still fails, try a more aggressive approach for specific extraction types
                                if self.extraction_type == "creditInfo":
                                    # For credit info, try to extract the deposits array
                                    try:
                                        # Create a simple JSON structure with just the deposits array
                                        deposits_match = re.search(r'"deposits"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                        if deposits_match:
                                            deposits_text = deposits_match.group(1).strip()
                                            # Fix any truncated JSON objects in the array
                                            deposits_text = re.sub(r',\s*$', '', deposits_text)  # Remove trailing commas
                                            deposits_json = f'{{"deposits":[{deposits_text}]}}'
                                            parsed_content = json.loads(deposits_json)
                                            response_dict['parsed_content'] = parsed_content
                                            logging.info("Successfully extracted deposits array from truncated JSON")
                                        else:
                                            # Try with CreditsInfo instead
                                            credits_match = re.search(r'"CreditsInfo"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                            if credits_match:
                                                credits_text = credits_match.group(1).strip()
                                                credits_text = re.sub(r',\s*$', '', credits_text)
                                                credits_json = f'{{"CreditsInfo":[{credits_text}]}}'
                                                parsed_content = json.loads(credits_json)
                                                response_dict['parsed_content'] = parsed_content
                                                logging.info("Successfully extracted CreditsInfo array from truncated JSON")
                                            else:
                                                raise Exception("Could not find deposits or CreditsInfo array in JSON")
                                    except Exception as e3:
                                        logging.warning(f"Could not extract credits array: {str(e3)}")
                                        raise e2
                                elif self.extraction_type == "checkNumberInorder":
                                    # For check number info, try to extract the CheckNumberinOrder array
                                    try:
                                        # Look for the CheckNumberinOrder array
                                        match = re.search(r'"CheckNumberinOrder"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                        if match:
                                            array_content = match.group(1).strip()
                                            # Fix any truncated JSON objects in the array
                                            array_content = re.sub(r',\s*$', '', array_content)  # Remove trailing commas

                                            # Try to fix the array content by completing any incomplete objects
                                            array_items = []
                                            current_item = ""
                                            brace_count = 0

                                            # First try to parse the whole array
                                            try:
                                                check_json = f'{{"CheckNumberinOrder":[{array_content}]}}'
                                                parsed_content = json.loads(check_json)
                                                response_dict['parsed_content'] = parsed_content
                                                logging.info("Successfully extracted CheckNumberinOrder array from truncated JSON")
                                                return
                                            except json.JSONDecodeError:
                                                # If that fails, try to parse each object individually
                                                logging.info("Trying to parse individual check objects from array")

                                                # Split the array content by commas outside of objects
                                                items = []
                                                current = ""
                                                depth = 0

                                                for char in array_content:
                                                    if char == '{':
                                                        depth += 1
                                                    elif char == '}':
                                                        depth -= 1

                                                    current += char

                                                    if depth == 0 and char == '}':
                                                        items.append(current)
                                                        current = ""
                                                    elif depth == 0 and char == ',':
                                                        current = ""

                                                # Try to parse each item
                                                for item in items:
                                                    try:
                                                        obj = json.loads(item)
                                                        array_items.append(obj)
                                                    except json.JSONDecodeError:
                                                        logging.warning(f"Skipping invalid check object: {item}")

                                                if array_items:
                                                    parsed_content = {"CheckNumberinOrder": array_items}
                                                    response_dict['parsed_content'] = parsed_content
                                                    logging.info(f"Successfully extracted {len(array_items)} valid items from CheckNumberinOrder array")
                                                else:
                                                    raise Exception("Could not extract any valid check objects from array")
                                        else:
                                            raise Exception("Could not find CheckNumberinOrder array in JSON")
                                    except Exception as e3:
                                        logging.warning(f"Could not extract CheckNumberinOrder array: {str(e3)}")
                                        raise e2
                                elif self.extraction_type == "debitInfo":
                                    # For debit info, try to extract the DebitInfo array
                                    try:
                                        match = re.search(r'"DebitInfo"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                        if match:
                                            array_content = match.group(1).strip()
                                            array_content = re.sub(r',\s*$', '', array_content)
                                            debit_json = f'{{"DebitInfo":[{array_content}]}}'
                                            parsed_content = json.loads(debit_json)
                                            response_dict['parsed_content'] = parsed_content
                                            logging.info("Successfully extracted DebitInfo array from truncated JSON")
                                        else:
                                            raise Exception("Could not find DebitInfo array in JSON")
                                    except Exception as e3:
                                        logging.warning(f"Could not extract DebitInfo array: {str(e3)}")
                                        raise e2
                                elif self.extraction_type == "dailyEndingBalance":
                                    # For daily ending balance, try to extract the dailyEndingBalance array
                                    try:
                                        match = re.search(r'"dailyEndingBalance"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                        if match:
                                            array_content = match.group(1).strip()
                                            array_content = re.sub(r',\s*$', '', array_content)
                                            balance_json = f'{{"dailyEndingBalance":[{array_content}]}}'
                                            parsed_content = json.loads(balance_json)
                                            response_dict['parsed_content'] = parsed_content
                                            logging.info("Successfully extracted dailyEndingBalance array from truncated JSON")
                                        else:
                                            raise Exception("Could not find dailyEndingBalance array in JSON")
                                    except Exception as e3:
                                        logging.warning(f"Could not extract dailyEndingBalance array: {str(e3)}")
                                        raise e2
                                else:
                                    # For other types, just raise the original error
                                    raise e2
                except json.JSONDecodeError as e:
                    # If it's not valid JSON, keep it as a string
                    logging.warning(f"Could not parse response content as JSON: {content_text[:100]}... Error: {str(e)}")
            response_dict['system_info'] = {
                    "hostname": socket.gethostname(),
                    "ip_address": socket.gethostbyname(socket.gethostname()),
                    "platform": platform.system(),
                    "platform_version": platform.version(),
                    "processor": platform.processor(),
                    "python_version": platform.python_version(),
                    "timestamp": datetime.datetime.now().isoformat(),
                    "user": os.getlogin() if hasattr(os, 'getlogin') else "unknown"
                }
            # Save the extracted data
            with open(response_file_path, 'w', encoding='utf-8') as file:
                json.dump(response_dict, file, ensure_ascii=False, indent=4)

            logging.info(f"Response saved to {response_file_path}")

            # Save parsed content to TrueData folder or custom response directory
            if parsed_content:
                if response_dir:
                    # Use the custom response directory
                    true_data_path = response_dir
                else:
                    # Create TrueData folder structure if it doesn't exist
                    true_data_base_path = "data/TrueData"
                    os.makedirs(true_data_base_path, exist_ok=True)

                    # Create subfolder for extraction type if it doesn't exist
                    for folder in ["creditInfo", "debitInfo", "checkNumberInorder", "dailyEndingBalance", "OpeningPageInfo"]:
                        os.makedirs(os.path.join(true_data_base_path, folder), exist_ok=True)

                    # Use the default TrueData path
                    true_data_path = os.path.join(true_data_base_path, self.extraction_type)

                # Ensure the directory exists
                os.makedirs(true_data_path, exist_ok=True)

                # Save parsed content to the corresponding subfolder
                true_data_file_path = os.path.join(
                    true_data_path,
                    f"{self.file_name_without_ext}_trueData.json"
                )

                with open(true_data_file_path, 'w', encoding='utf-8') as file:
                    json.dump(parsed_content, file, ensure_ascii=False, indent=4)

                logging.info(f"Parsed content saved to {true_data_file_path}")

                # Also save to the response directory if provided
                if response_dir:
                    response_file_path = os.path.join(
                        response_dir,
                        f"{self.file_name_without_ext}_trueData.json"
                    )

                    with open(response_file_path, 'w', encoding='utf-8') as file:
                        json.dump(parsed_content, file, ensure_ascii=False, indent=4)

                    logging.info(f"Parsed content also saved to {response_file_path}")

        except Exception as e:
            logging.error(f"Error saving response: {e}")

    def move_file(self) -> None:
        """Move processed files to the processed folder."""
        # Create processed folder if it doesn't exist
        processed_path = os.path.join(self.folder_path, PROCESSED_FOLDER_NAME)
        os.makedirs(processed_path, exist_ok=True)

        try:
            # Move PDF file
            pdf_dest = os.path.join(processed_path, self.file_name)
            if os.path.exists(pdf_dest):
                os.remove(pdf_dest)  # Remove existing file if it exists
            shutil.move(self.file_path, processed_path)

            # Move folder containing extracted text
            source_folder = os.path.join(self.folder_path, self.file_name_without_ext)
            dest_folder = os.path.join(processed_path, self.file_name_without_ext)

            if os.path.exists(source_folder):
                if os.path.exists(dest_folder):
                    # If destination exists, remove it first
                    shutil.rmtree(dest_folder)
                shutil.move(source_folder, processed_path)

            logging.info(f"Moved files to {processed_path}")
        except Exception as e:
            logging.error(f"Error moving files: {e}")


class DocumentProcessor:
    """Handles the processing of documents."""

    def __init__(self, extraction_type: str) -> None:
        """Initialize the document processor."""
        self.config = Config.load_config()
        self.extraction_type = extraction_type

    async def process_document(self, file_path: str, response_dir: str = None) -> None:
        """
        Process a single document.

        Args:
            file_path: Path to the PDF file to process
            response_dir: Optional custom directory to save the parsed content
        """
        logging.info(f"Processing {file_path}")

        # Check if the PDF has multiple pages
        try:
            reader = PdfReader(file_path)
            total_pages = len(reader.pages)

            if total_pages > 1:
                logging.info(f"PDF has {total_pages} pages. Processing each page individually.")

                # Create MultiPageResponses subfolder in the PDFs directory
                pdfs_dir = os.path.dirname(file_path)
                multi_page_dir = os.path.join(pdfs_dir, "MultiPageResponses")
                os.makedirs(multi_page_dir, exist_ok=True)

                # Create subfolder for this specific PDF
                pdf_name = os.path.splitext(os.path.basename(file_path))[0]
                pdf_response_dir = os.path.join(multi_page_dir, pdf_name)
                os.makedirs(pdf_response_dir, exist_ok=True)

                # Process each page individually
                page_responses = []
                page_tasks = []

                for i in range(total_pages):
                    # Create a single-page PDF for each page
                    page_pdf_path = os.path.join(pdf_response_dir, f"{pdf_name}_page_{i+1}.pdf")
                    writer = PdfWriter()
                    writer.add_page(reader.pages[i])
                    with open(page_pdf_path, "wb") as output_file:
                        writer.write(output_file)

                    # Create a task to process this page
                    page_tasks.append(self._process_single_page(
                        page_pdf_path,
                        self.extraction_type,
                        pdf_response_dir
                    ))

                # Process all pages concurrently
                page_responses = await asyncio.gather(*page_tasks)

                # Merge the responses
                merged_response = self._merge_page_responses(page_responses, self.extraction_type)

                # Save the merged response
                if merged_response:
                    # Save to the original response directory
                    if response_dir:
                        merged_file_path = os.path.join(
                            response_dir,
                            f"{pdf_name}_trueData.json"
                        )
                        with open(merged_file_path, 'w', encoding='utf-8') as file:
                            json.dump(merged_response, file, ensure_ascii=False, indent=4)
                        logging.info(f"Merged response saved to {merged_file_path}")

                    # Also save to the multi-page response directory
                    merged_file_path = os.path.join(
                        pdf_response_dir,
                        f"{pdf_name}_merged_trueData.json"
                    )
                    with open(merged_file_path, 'w', encoding='utf-8') as file:
                        json.dump(merged_response, file, ensure_ascii=False, indent=4)
                    logging.info(f"Merged response also saved to {merged_file_path}")

                # Don't move the original file since we're processing it page by page
                logging.info(f"Completed processing multi-page PDF {file_path}")
                return

            # If it's a single-page PDF, process it normally
            extractor = OpenAIExtractor(file_path, self.extraction_type)
            await extractor.load_configurations()
            await extractor.call_openai_api()
            extractor.save_response(response_dir)
            extractor.move_file()

        except Exception as e:
            logging.error(f"Error processing document {file_path}: {e}")
            logging.error(traceback.format_exc())

        logging.info(f"Completed processing {file_path}")

    async def _process_single_page(self, page_pdf_path: str, extraction_type: str, response_dir: str) -> dict:
        """
        Process a single page of a PDF.

        Args:
            page_pdf_path: Path to the single-page PDF file
            extraction_type: Type of extraction to perform
            response_dir: Directory to save the response

        Returns:
            The parsed content from OpenAI
        """
        try:
            logging.info(f"Processing single page: {page_pdf_path}")

            # Create an extractor for this page
            extractor = OpenAIExtractor(page_pdf_path, extraction_type)
            await extractor.load_configurations()
            await extractor.call_openai_api()

            # Save the response to the multi-page response directory
            extractor.save_response(response_dir)

            # Also save the raw OpenAI response to the multi-page directory
            if hasattr(extractor, 'response'):
                # Create a filename for the OpenAI response
                page_name = os.path.splitext(os.path.basename(page_pdf_path))[0]
                openai_response_filename = os.path.join(
                    response_dir, f"{page_name}_openai_response.json"
                )

                # Convert the response to a serializable format
                response_dict = {}

                # Handle o4-mini model response structure
                if hasattr(extractor.response, 'model'):
                    response_dict['model'] = extractor.response.model

                # Extract content from the response
                content_text = None

                # Try to extract from o4-mini response structure
                if hasattr(extractor.response, 'output') and len(extractor.response.output) > 0:
                    for output_item in extractor.response.output:
                        if hasattr(output_item, 'content') and len(output_item.content) > 0:
                            for content_item in output_item.content:
                                if hasattr(content_item, 'text'):
                                    content_text = content_item.text
                                    break
                            if content_text:
                                break

                # Fallback to traditional structure if o4-mini structure not found
                if not content_text and hasattr(extractor.response, 'choices') and len(extractor.response.choices) > 0:
                    if hasattr(extractor.response.choices[0], 'message') and hasattr(extractor.response.choices[0].message, 'content'):
                        content_text = extractor.response.choices[0].message.content

                # Store the content in the response dictionary
                if content_text:
                    response_dict['content'] = content_text

                # Save the OpenAI response
                with open(openai_response_filename, 'w', encoding='utf-8') as file:
                    json.dump(response_dict, file, ensure_ascii=False, indent=4)

                logging.info(f"OpenAI response saved to {openai_response_filename}")

            # Get the parsed content
            if hasattr(extractor, 'response'):
                # Extract content from the response
                content_text = None

                # Try to extract from o4-mini response structure
                if hasattr(extractor.response, 'output') and len(extractor.response.output) > 0:
                    for output_item in extractor.response.output:
                        if hasattr(output_item, 'content') and len(output_item.content) > 0:
                            for content_item in output_item.content:
                                if hasattr(content_item, 'text'):
                                    content_text = content_item.text
                                    break
                            if content_text:
                                break

                # Fallback to traditional structure if o4-mini structure not found
                if not content_text and hasattr(extractor.response, 'choices') and len(extractor.response.choices) > 0:
                    if hasattr(extractor.response.choices[0], 'message') and hasattr(extractor.response.choices[0].message, 'content'):
                        content_text = extractor.response.choices[0].message.content

                # Parse the content as JSON if it's a string
                if content_text and isinstance(content_text, str):
                    # Clean up the content text - remove markdown code block markers if present
                    cleaned_text = content_text

                    # Remove ```json at the beginning if present
                    if cleaned_text.strip().startswith("```json"):
                        cleaned_text = cleaned_text.strip()[7:].strip()

                    # Remove ``` at the end if present
                    if cleaned_text.strip().endswith("```"):
                        cleaned_text = cleaned_text.strip()[:-3].strip()

                    try:
                        parsed_content = json.loads(cleaned_text)
                        return parsed_content
                    except json.JSONDecodeError as e:
                        logging.error(f"Failed to parse JSON from page response: {cleaned_text[:100]}... Error: {str(e)}")

                        # Try to fix truncated JSON by adding missing closing brackets
                        fixed_text = cleaned_text

                        # Count opening and closing braces/brackets
                        open_braces = fixed_text.count('{')
                        close_braces = fixed_text.count('}')
                        open_brackets = fixed_text.count('[')
                        close_brackets = fixed_text.count(']')

                        # Add missing closing braces/brackets
                        if open_braces > close_braces:
                            fixed_text += '}' * (open_braces - close_braces)
                        if open_brackets > close_brackets:
                            fixed_text += ']' * (open_brackets - close_brackets)

                        # Try parsing again with the fixed text
                        try:
                            parsed_content = json.loads(fixed_text)
                            logging.info("Successfully fixed and parsed JSON by adding missing brackets")
                            return parsed_content
                        except json.JSONDecodeError:
                            # If it still fails, try a more aggressive approach
                            logging.warning("Still failed to parse JSON after adding missing brackets. Trying more aggressive fixes...")

                            # Try to extract the main array based on extraction type
                            if extraction_type == "checkNumberInorder":
                                try:
                                    # Look for the CheckNumberinOrder array
                                    match = re.search(r'"CheckNumberinOrder"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                    if match:
                                        array_content = match.group(1).strip()
                                        # Fix any truncated JSON objects in the array
                                        array_content = re.sub(r',\s*$', '', array_content)  # Remove trailing commas

                                        # Try to fix the array content by completing any incomplete objects
                                        array_items = []
                                        current_item = ""
                                        brace_count = 0

                                        for char in array_content:
                                            current_item += char
                                            if char == '{':
                                                brace_count += 1
                                            elif char == '}':
                                                brace_count -= 1
                                                if brace_count == 0:
                                                    # We have a complete object
                                                    try:
                                                        item_obj = json.loads(current_item)
                                                        array_items.append(item_obj)
                                                    except json.JSONDecodeError:
                                                        logging.warning(f"Skipping invalid JSON object: {current_item}")
                                                    current_item = ""

                                        # Create a valid JSON with the array
                                        if array_items:
                                            valid_json = {"CheckNumberinOrder": array_items}
                                            logging.info(f"Successfully extracted {len(array_items)} valid items from CheckNumberinOrder array")
                                            return valid_json
                                except Exception as ex:
                                    logging.error(f"Error during aggressive JSON fixing: {str(ex)}")

                            # Similar approach for other extraction types
                            elif extraction_type == "creditInfo":
                                try:
                                    match = re.search(r'"CreditsInfo"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                    if match:
                                        array_content = match.group(1).strip()
                                        array_content = re.sub(r',\s*$', '', array_content)

                                        # Try to fix the array content by completing any incomplete objects
                                        array_items = []
                                        current_item = ""
                                        brace_count = 0

                                        for char in array_content:
                                            current_item += char
                                            if char == '{':
                                                brace_count += 1
                                            elif char == '}':
                                                brace_count -= 1
                                                if brace_count == 0:
                                                    # We have a complete object
                                                    try:
                                                        item_obj = json.loads(current_item)
                                                        array_items.append(item_obj)
                                                    except json.JSONDecodeError:
                                                        logging.warning(f"Skipping invalid JSON object: {current_item}")
                                                    current_item = ""

                                        # Create a valid JSON with the array
                                        if array_items:
                                            valid_json = {"CreditsInfo": array_items}
                                            logging.info(f"Successfully extracted {len(array_items)} valid items from CreditsInfo array")
                                            return valid_json
                                except Exception as ex:
                                    logging.error(f"Error during aggressive JSON fixing: {str(ex)}")

                            elif extraction_type == "debitInfo":
                                try:
                                    match = re.search(r'"DebitInfo"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                    if match:
                                        array_content = match.group(1).strip()
                                        array_content = re.sub(r',\s*$', '', array_content)

                                        # Try to fix the array content by completing any incomplete objects
                                        array_items = []
                                        current_item = ""
                                        brace_count = 0

                                        for char in array_content:
                                            current_item += char
                                            if char == '{':
                                                brace_count += 1
                                            elif char == '}':
                                                brace_count -= 1
                                                if brace_count == 0:
                                                    # We have a complete object
                                                    try:
                                                        item_obj = json.loads(current_item)
                                                        array_items.append(item_obj)
                                                    except json.JSONDecodeError:
                                                        logging.warning(f"Skipping invalid JSON object: {current_item}")
                                                    current_item = ""

                                        # Create a valid JSON with the array
                                        if array_items:
                                            valid_json = {"DebitInfo": array_items}
                                            logging.info(f"Successfully extracted {len(array_items)} valid items from DebitInfo array")
                                            return valid_json
                                except Exception as ex:
                                    logging.error(f"Error during aggressive JSON fixing: {str(ex)}")

                            elif extraction_type == "dailyEndingBalance":
                                try:
                                    match = re.search(r'"dailyEndingBalance"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                    if match:
                                        array_content = match.group(1).strip()
                                        array_content = re.sub(r',\s*$', '', array_content)

                                        # Try to fix the array content by completing any incomplete objects
                                        array_items = []
                                        current_item = ""
                                        brace_count = 0

                                        for char in array_content:
                                            current_item += char
                                            if char == '{':
                                                brace_count += 1
                                            elif char == '}':
                                                brace_count -= 1
                                                if brace_count == 0:
                                                    # We have a complete object
                                                    try:
                                                        item_obj = json.loads(current_item)
                                                        array_items.append(item_obj)
                                                    except json.JSONDecodeError:
                                                        logging.warning(f"Skipping invalid JSON object: {current_item}")
                                                    current_item = ""

                                        # Create a valid JSON with the array
                                        if array_items:
                                            valid_json = {"dailyEndingBalance": array_items}
                                            logging.info(f"Successfully extracted {len(array_items)} valid items from dailyEndingBalance array")
                                            return valid_json
                                except Exception as ex:
                                    logging.error(f"Error during aggressive JSON fixing: {str(ex)}")

                            # If all attempts fail, return an empty object
                            logging.error("All JSON parsing attempts failed. Returning empty object.")

            return {}

        except Exception as e:
            logging.error(f"Error processing single page {page_pdf_path}: {e}")
            logging.error(traceback.format_exc())
            return {}

    def _merge_page_responses(self, page_responses: list, extraction_type: str) -> dict:
        """
        Merge responses from multiple pages into a single response.

        Args:
            page_responses: List of parsed responses from individual pages
            extraction_type: Type of extraction performed

        Returns:
            Merged response
        """
        if not page_responses:
            return {}

        try:
            # Filter out empty responses
            valid_responses = [resp for resp in page_responses if resp]

            if not valid_responses:
                return {}

            # Initialize the merged response with the structure of the first valid response
            merged_response = {}

            # Handle different extraction types
            if extraction_type == "creditInfo":
                # Merge CreditsInfo arrays
                merged_response = valid_responses[0].copy()  # Start with the first response
                all_credits = []

                # Collect all credit entries from all pages
                for resp in valid_responses:
                    credits = resp.get("CreditsInfo", [])
                    if isinstance(credits, list):
                        all_credits.extend(credits)

                # Update the merged response with all credits
                merged_response["CreditsInfo"] = all_credits

            elif extraction_type == "debitInfo":
                # Merge DebitInfo arrays
                merged_response = valid_responses[0].copy()  # Start with the first response
                all_debits = []

                # Collect all debit entries from all pages
                for resp in valid_responses:
                    debits = resp.get("DebitInfo", [])
                    if isinstance(debits, list):
                        all_debits.extend(debits)

                # Update the merged response with all debits
                merged_response["DebitInfo"] = all_debits

            elif extraction_type == "checkNumberInorder":
                # Merge CheckNumberinOrder arrays
                merged_response = valid_responses[0].copy()  # Start with the first response
                all_checks = []

                # Collect all check entries from all pages
                for resp in valid_responses:
                    checks = resp.get("CheckNumberinOrder", [])
                    if isinstance(checks, list):
                        all_checks.extend(checks)

                # Update the merged response with all checks
                merged_response["CheckNumberinOrder"] = all_checks

            elif extraction_type == "dailyEndingBalance":
                # Merge dailyEndingBalance arrays
                merged_response = valid_responses[0].copy()  # Start with the first response
                all_balances = []

                # Collect all balance entries from all pages
                for resp in valid_responses:
                    balances = resp.get("dailyEndingBalance", [])
                    if isinstance(balances, list):
                        all_balances.extend(balances)

                # Update the merged response with all balances
                merged_response["dailyEndingBalance"] = all_balances

            elif extraction_type == "OpeningPageInfo":
                # For opening page, just use the first valid response
                merged_response = valid_responses[0]

            logging.info(f"Successfully merged {len(valid_responses)} page responses for {extraction_type}")
            return merged_response

        except Exception as e:
            logging.error(f"Error merging page responses: {e}")
            logging.error(traceback.format_exc())
            return {}

    async def process_all_documents(self, files_to_process: int = 1) -> None:
        """Process multiple documents."""
        folder_path = os.path.join(self.config.get("inputDataPath", "data/inputData"), self.extraction_type)
        logging.info(f"Processing documents in {folder_path}")

        files_processed = 0

        try:
            for file in os.listdir(folder_path):
                if file.lower().endswith(".pdf"):
                    file_path = os.path.join(folder_path, file)
                    await self.process_document(file_path)

                    files_processed += 1
                    if files_processed >= files_to_process:
                        break
        except Exception as e:
            logging.error(f"Error processing documents: {e}")
            traceback.print_exc()


async def extract_by_aws_textract(local_pdf_path: str) -> Dict:
    """Extract text and tables from a PDF file using AWS Textract."""
    # Configuration
    bucket_name = 'testingparagtraders'  # Replace with your S3 bucket name
    object_name = None  # Optionally specify the S3 object name
    job_tag = "ExtractTextJob"
    notification_channel = None  # Replace with your SNS topic ARN if needed

    # Derive base filename from the input PDF file name
    base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]

    # Create output folder
    output_folder = os.path.join(os.path.dirname(local_pdf_path), base_filename)
    os.makedirs(output_folder, exist_ok=True)

    # Check if this is a page from a multi-page PDF
    is_multi_page = False
    multi_page_dir = None
    if "MultiPageResponses" in local_pdf_path:
        is_multi_page = True
        # Get the parent directory (which should be the PDF name folder)
        multi_page_dir = os.path.dirname(local_pdf_path)

    # Initialize AWS clients
    session = aioboto3.Session()
    async with session.client('s3') as s3_client, session.client('textract') as textract_client:
        # Upload PDF to S3
        logging.info(f"Uploading {local_pdf_path} to S3...")
        s3_key = await AWSTextract.upload_to_s3(s3_client, local_pdf_path, bucket_name, object_name)

        # Step 1: Start text detection for regular text
        logging.info("Starting text detection with Textract...")
        text_job_id = await AWSTextract.start_document_text_detection(
            textract_client, bucket_name, s3_key, job_tag, notification_channel
        )

        # Step 2: Start document analysis for tables
        logging.info("Starting document analysis for tables with Textract...")
        table_job_id = await AWSTextract.start_document_analysis(
            textract_client, bucket_name, s3_key, f"{job_tag}_Tables", notification_channel
        )

        # Get text detection results
        logging.info("Polling for text detection results...")
        text_blocks = await AWSTextract.get_document_text_detection(textract_client, text_job_id)

        # Get document analysis results (for tables)
        logging.info("Polling for document analysis results (tables)...")
        table_blocks = await AWSTextract.get_document_analysis(textract_client, table_job_id)

        # Extract text in reading order
        logging.info("Extracting text in reading order...")
        ordered_text = AWSTextract.get_text_in_order(text_blocks)

        # Extract tables
        logging.info("Extracting tables from results...")
        tables = AWSTextract.extract_tables_from_blocks(table_blocks)
        logging.info(f"Found {len(tables)} table(s).")

        # Filter out text that appears in tables to avoid duplicates
        logging.info("Filtering out text that appears in tables...")
        filtered_ordered_text = filter_text_by_tables(ordered_text, tables)
        logging.info(f"Filtered out {len(ordered_text) - len(filtered_ordered_text)} text items that appear in tables.")

        # Save extracted text and tables to file
        text_json_filename = os.path.join(
            output_folder, f"{base_filename}{TEXT_CONVERSION_SUFFIX}.txt"
        )

        try:
            # Create combined text file with both text and tables
            async with aiofiles.open(text_json_filename, 'w', encoding='utf-8') as f:
                # Write Text section
                await f.write("Text\n")

                # Format the text in CSV-like format
                csv_content = "Page No., Text, X1, Y1, X2, Y2\n"
                for item in filtered_ordered_text:  # Use filtered text here
                    csv_content += f"{item['Page']}, {item['Text']}, {item['X1']}, {item['Y1']}, {item['X2']}, {item['Y2']}\n"

                await f.write(csv_content)
                await f.write("\n\n")

                # Write Tables section
                for idx, table in enumerate(tables, start=1):
                    await f.write(f"Table-{idx}\n")

                    # Use StringIO to accumulate the CSV data in memory
                    output = StringIO()
                    import csv
                    writer = csv.writer(output)

                    # Write each row of the table
                    for row in table:
                        # Replace empty cells with '0'
                        new_row = [cell if cell.strip() != '' else '0' for cell in row]
                        writer.writerow(new_row)

                    # Get the CSV content as a string
                    table_data = output.getvalue()

                    # Write the table data to the file
                    await f.write(table_data)
                    await f.write("\n\n")

            # If this is a page from a multi-page PDF, also save the AWS Textract responses to the multi-page directory
            if is_multi_page and multi_page_dir:
                # Save the AWS Textract text response
                aws_text_response_filename = os.path.join(
                    multi_page_dir, f"{base_filename}_aws_text_response.json"
                )

                # Convert blocks to a serializable format
                serializable_text_blocks = []
                for block in text_blocks:
                    # Convert any non-serializable objects to strings or simple types
                    serializable_block = {}
                    for key, value in block.items():
                        if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                            serializable_block[key] = value
                        else:
                            serializable_block[key] = str(value)
                    serializable_text_blocks.append(serializable_block)

                # Save the AWS text response
                async with aiofiles.open(aws_text_response_filename, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(serializable_text_blocks, ensure_ascii=False, indent=4))

                logging.info(f"AWS Textract text response saved to {aws_text_response_filename}")

                # Save the AWS Textract table response
                aws_table_response_filename = os.path.join(
                    multi_page_dir, f"{base_filename}_aws_table_response.json"
                )

                # Convert blocks to a serializable format
                serializable_table_blocks = []
                for block in table_blocks:
                    # Convert any non-serializable objects to strings or simple types
                    serializable_block = {}
                    for key, value in block.items():
                        if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                            serializable_block[key] = value
                        else:
                            serializable_block[key] = str(value)
                    serializable_table_blocks.append(serializable_block)

                # Save the AWS table response
                async with aiofiles.open(aws_table_response_filename, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(serializable_table_blocks, ensure_ascii=False, indent=4))

                logging.info(f"AWS Textract table response saved to {aws_table_response_filename}")

            logging.info(f"Extracted text and tables saved to {text_json_filename}.")
            return {"status": "success", "file_path": text_json_filename}
        except Exception as e:
            logging.error(f"Error writing to file: {e}")
            logging.error(traceback.format_exc())
            return {"status": "error", "error": str(e)}


async def process_bank_statement(input_pdf_path: str) -> str:
    """
    Process a bank statement PDF by separating it into multiple PDFs and extracting data from each.

    Args:
        input_pdf_path: Path to the input PDF file

    Returns:
        Path to the timestamped directory containing the results
    """
    try:
        import socket
        import platform
        # Create a new timestamp for each file processing with microseconds and a random suffix for uniqueness
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # Add a random suffix to ensure uniqueness even if multiple files are processed in the same second
        random_suffix = str(uuid.uuid4())[:8]
        timestamp = f"{timestamp}_{random_suffix}"
        logging.info(f"Generated new unique timestamp for processing: {timestamp}")

        # Create base directory
        base_dir = "Data1"
        os.makedirs(base_dir, exist_ok=True)

        # Create timestamped directory
        timestamped_dir = os.path.join(base_dir, timestamp)
        os.makedirs(timestamped_dir, exist_ok=True)

        # Create subdirectories
        pdfs_dir = os.path.join(timestamped_dir, "PDFs")
        input_pdf_dir = os.path.join(timestamped_dir, "inputPDF")
        response_dir = os.path.join(timestamped_dir, "response")
        output_dir = os.path.join(timestamped_dir, "output")  # For Excel output
        api_responses_dir = os.path.join(timestamped_dir, "apiResponses")

        os.makedirs(pdfs_dir, exist_ok=True)
        os.makedirs(input_pdf_dir, exist_ok=True)
        os.makedirs(response_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(api_responses_dir, exist_ok=True)

        # Save system information
        system_info = {
            "hostname": socket.gethostname(),
            "ip_address": socket.gethostbyname(socket.gethostname()),
            "platform": platform.system(),
            "platform_version": platform.version(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "timestamp": datetime.datetime.now().isoformat(),
            "user": os.getlogin() if hasattr(os, 'getlogin') else "unknown"
        }
        # Save system info to a JSON file
        system_info_path = os.path.join(api_responses_dir, "system_info.json")
        with open(system_info_path, 'w') as f:
            json.dump(system_info, f, indent=4)

        logging.info(f"System information saved to {system_info_path}")

        # Copy the input PDF to the inputPDF directory
        input_pdf_filename = os.path.basename(input_pdf_path)
        input_pdf_copy_path = os.path.join(input_pdf_dir, input_pdf_filename)
        shutil.copy2(input_pdf_path, input_pdf_copy_path)
        logging.info(f"Copied input PDF to {input_pdf_copy_path}")

        # Separate the PDF into multiple PDFs
        logging.info(f"Separating PDF into multiple PDFs...")
        separated_pdfs = separate_pdf(input_pdf_path, pdfs_dir, debug=True)

        if not separated_pdfs:
            logging.error("Failed to separate PDF into multiple PDFs")
            return None

        logging.info(f"Successfully separated PDF into {len(separated_pdfs)} PDFs")

        # Create tasks for concurrent processing
        processing_tasks = []

        # Create tasks for each PDF file
        for pdf_path in separated_pdfs:
            pdf_filename = os.path.basename(pdf_path)

            # Determine the extraction type based on the filename
            extraction_type = None
            if "CreditPages" in pdf_filename:
                extraction_type = "creditInfo"
            elif "DebitPages" in pdf_filename:
                extraction_type = "debitInfo"
            elif "CheckPages" in pdf_filename:
                extraction_type = "checkNumberInorder"
            elif "EndingBalance" in pdf_filename:
                extraction_type = "dailyEndingBalance"
            elif "OpeningPage" in pdf_filename:
                extraction_type = "OpeningPageInfo"
            else:
                logging.info(f"Skipping {pdf_filename} as it doesn't match any extraction type")
                continue

            logging.info(f"Preparing task for {pdf_filename} with extraction type: {extraction_type}")

            # Create a document processor for the extraction type
            processor = DocumentProcessor(extraction_type)

            # Add task to the list
            processing_tasks.append(processor.process_document(pdf_path, response_dir))

        # Process all PDFs concurrently
        if processing_tasks:
            start_time = time.time()
            logging.info(f"Starting concurrent processing of {len(processing_tasks)} PDFs...")

            # Execute all tasks concurrently
            await asyncio.gather(*processing_tasks)

            end_time = time.time()
            logging.info(f"Concurrent processing completed in {end_time - start_time:.2f} seconds")
        else:
            logging.warning("No PDFs to process")
            return None

        logging.info(f"Completed processing all separated PDFs")
        logging.info(f"Results saved to {timestamped_dir}")

        # Return the absolute path to the timestamped directory
        return os.path.abspath(timestamped_dir)

    except Exception as e:
        logging.error(f"Error in process_bank_statement: {e}")
        logging.error(traceback.format_exc())
        return None


async def main(input_pdf: str = None):
    """
    Main entry point for the application.

    Args:
        input_pdf: Path to the input PDF file
    """
    # Use the provided input_pdf or default to a sample file
    if input_pdf is None:
        clientName = "XAI"
        input_pdf = r"C:\Users\<USER>\Downloads\test\1 Blue 65 Bank Statement January 2024.pdf"  # Default path to the input PDF file

    if not os.path.exists(input_pdf):
        logging.error(f"Input PDF file '{input_pdf}' does not exist")
        return None

    # Process the bank statement
    start_time = time.time()
    logging.info(f"Starting bank statement processing for: {input_pdf}")

    results_dir = await process_bank_statement(input_pdf)

    if results_dir:
        processing_time = time.time() - start_time
        logging.info(f"Bank statement processing completed in {processing_time:.2f} seconds")
        logging.info(f"Results saved to: {results_dir}")
    else:
        logging.error("Bank statement processing failed")

    return results_dir


if __name__ == "__main__":
    # Get input PDF path from command line arguments
    if len(sys.argv) > 1:
        input_pdf = sys.argv[1]
        asyncio.run(main(input_pdf))
    else:
        # Run with default PDF path
        asyncio.run(main())
