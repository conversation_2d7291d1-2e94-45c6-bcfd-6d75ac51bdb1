#!/usr/bin/env python3
"""
API Server for US Bank Statement Processor

This script creates a Flask API server that connects the frontend to the bank statement processing functionality.
It accepts PDF file uploads, processes them using the existing functionality, and returns the Excel file for download.
"""

import os
import sys
import asyncio
import logging
import time
import traceback
import uuid
from pathlib import Path
from typing import Optional
from flask import Flask, request, jsonify, send_file, after_this_request
from flask_cors import CORS
import shutil
from PyPDF2 import PdfReader

# Import the process_bank_statement function from bankStatement.py
from src.bankStatement import process_bank_statement

# Import the Excel report generation function
try:
    from src.excel import process_json_and_generate_report
except ImportError:
    process_json_and_generate_report = None
    print("Warning: Excel module not found. Excel report generation will be disabled.")

# Configure logging
def setup_logging():
    """Set up logging to both console and file."""
    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Use a single log file named "log"
    log_file = os.path.join(logs_dir, "log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers to prevent duplicate logs
    if logger.handlers:
        logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create file handler with append mode
    file_handler = logging.FileHandler(log_file, mode='a')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Set up logging
logger = setup_logging()
logger.info(f"API Server logging initialized. Log file: {os.path.join('logs', os.path.basename(logger.handlers[0].baseFilename))}")

# Create Flask app
app = Flask(__name__)

# Configure CORS to allow requests from any origin
cors = CORS(app, resources={
    r"/*": {
        "origins": "*",
        "methods": ["GET", "POST", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"],
        "supports_credentials": True,
        "expose_headers": ["Content-Disposition"]
    }
})

# Create uploads directory
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), "uploads")
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Create a directory for temporary files
TEMP_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
os.makedirs(TEMP_FOLDER, exist_ok=True)

# Function to run async code in Flask
def run_async(coro):
    """Run an async coroutine in a synchronous context."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()

def get_pdf_page_count(pdf_path):
    """
    Get the number of pages in a PDF file.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        Number of pages in the PDF, or 0 if there's an error
    """
    try:
        reader = PdfReader(pdf_path)
        return len(reader.pages)
    except Exception as e:
        logger.error(f"Error getting PDF page count for {pdf_path}: {e}")
        return 0

@app.route('/api/process', methods=['POST'])
def process_pdf():
    """
    API endpoint to process a PDF file and return the Excel file.

    Expects a PDF file in the request.
    Returns the Excel file for download.
    """
    try:
        # Check if a file was uploaded
        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        # Check if the file is empty
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        # Check if the file is a PDF
        if not file.filename.lower().endswith('.pdf'):
            return jsonify({'error': 'File must be a PDF'}), 400

        # Generate a unique ID for this upload
        upload_id = str(uuid.uuid4())

        # Create a directory for this upload
        upload_dir = os.path.join(UPLOAD_FOLDER, upload_id)
        os.makedirs(upload_dir, exist_ok=True)

        # Save the uploaded file
        pdf_path = os.path.join(upload_dir, file.filename)
        file.save(pdf_path)

        logger.info(f"File uploaded: {pdf_path}")

        # Process the PDF file
        start_time = time.time()
        logger.info(f"Starting bank statement processing for: {pdf_path}")

        # Call the process_bank_statement function
        results_dir = run_async(process_bank_statement(pdf_path))

        if not results_dir:
            logger.error("Bank statement processing failed")
            return jsonify({'error': 'Processing failed'}), 500

        processing_time = time.time() - start_time
        logger.info(f"Bank statement processing completed in {processing_time:.2f} seconds")
        logger.info(f"Results saved to: {results_dir}")

        # Generate Excel report
        if process_json_and_generate_report:
            try:
                excel_start_time = time.time()
                logger.info(f"Generating Excel report for: {results_dir}")
                excel_file_path = process_json_and_generate_report(results_dir)
                excel_time = time.time() - excel_start_time
                logger.info(f"Excel report generated successfully in {excel_time:.2f} seconds")

                # Return the Excel file for download
                response = send_file(
                    excel_file_path,
                    as_attachment=True,
                    download_name=f"{os.path.splitext(file.filename)[0]}.xlsx",
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )

                # Clean up after sending the file
                @after_this_request
                def cleanup(response):
                    try:
                        # Clean up the upload directory after a delay
                        # This ensures the file is sent before cleanup
                        def delayed_cleanup():
                            time.sleep(5)  # Wait 5 seconds
                            shutil.rmtree(upload_dir, ignore_errors=True)

                        # Start cleanup in a separate thread
                        import threading
                        threading.Thread(target=delayed_cleanup).start()
                    except Exception as e:
                        logger.error(f"Error in cleanup: {e}")
                    return response

                return response
            except Exception as e:
                logger.error(f"Failed to generate Excel report: {e}")
                logger.error(traceback.format_exc())
                return jsonify({'error': 'Excel generation failed'}), 500
        else:
            return jsonify({'error': 'Excel generation module not available'}), 500

    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@app.route('/api/history', methods=['GET'])
def get_history():
    """
    API endpoint to get the system-wide processing history.

    Returns a list of all processed files with their details.
    """
    try:
        history = []
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")

        if not os.path.exists(data_dir):
            return jsonify({'history': []}), 200

        # List all timestamped directories in Data1
        for timestamp_dir in sorted(os.listdir(data_dir), reverse=True):
            timestamp_path = os.path.join(data_dir, timestamp_dir)

            # Skip if not a directory
            if not os.path.isdir(timestamp_path):
                continue

            # Check for input PDF
            input_pdf_dir = os.path.join(timestamp_path, "inputPDF")
            if not os.path.exists(input_pdf_dir):
                continue

            # Get the PDF file name
            pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
            if not pdf_files:
                continue

            # Check if Excel file was generated
            excel_file_path = None
            output_dir = os.path.join(timestamp_path, "output")
            if os.path.exists(output_dir):
                excel_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.xlsx')]
                if excel_files:
                    excel_file_path = os.path.join(output_dir, excel_files[0])

            # Get file size and page count
            pdf_path = os.path.join(input_pdf_dir, pdf_files[0])
            file_size = os.path.getsize(pdf_path)
            file_size_str = f"{file_size / 1024 / 1024:.2f} MB" if file_size > 1024 * 1024 else f"{file_size / 1024:.0f} KB"
            page_count = get_pdf_page_count(pdf_path)

            # Parse timestamp from directory name
            try:
                # Format: YYYYMMDD_HHMMSS_randomsuffix
                # Extract the datetime part (before the random suffix)
                timestamp_parts = timestamp_dir.split('_')
                if len(timestamp_parts) >= 2:
                    # Get the date and time parts
                    date_part = timestamp_parts[0]
                    time_part = timestamp_parts[1]

                    # Parse the date and time
                    year = int(date_part[0:4])
                    month = int(date_part[4:6])
                    day = int(date_part[6:8])
                    hour = int(time_part[0:2])
                    minute = int(time_part[2:4])
                    second = int(time_part[4:6])

                    # Create ISO format timestamp
                    timestamp = f"{year:04d}-{month:02d}-{day:02d}T{hour:02d}:{minute:02d}:{second:02d}"
                else:
                    # If the format doesn't match, use the directory name as timestamp
                    timestamp = timestamp_dir
            except (ValueError, IndexError):
                # If parsing fails, use the directory name as timestamp
                timestamp = timestamp_dir

            # Add to history
            history.append({
                "id": timestamp_dir,
                "fileName": pdf_files[0],
                "uploadDate": timestamp,
                "fileSize": file_size_str,
                "pageCount": page_count,
                "excelPath": excel_file_path
            })

        return jsonify({'history': history}), 200

    except Exception as e:
        logger.error(f"Error getting history: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """
    API endpoint to check if the server is running.

    Returns:
        A simple JSON response indicating the server is running.
    """
    return jsonify({
        'status': 'ok',
        'message': 'API server is running',
        'timestamp': time.time()
    }), 200

# API endpoint to check if an Excel file is available
@app.route('/api/check-excel/<timestamp_id>', methods=['GET'])
def check_excel_availability(timestamp_id):
    """
    API endpoint to check if an Excel file is available for a given timestamp.

    Args:
        timestamp_id: The timestamp directory ID

    Returns:
        JSON response with availability status and Excel file name
    """
    try:
        # Validate the timestamp ID
        if not timestamp_id or '..' in timestamp_id:
            logger.error(f"Invalid timestamp ID: {timestamp_id}")
            return jsonify({'error': 'Invalid timestamp ID', 'available': False}), 400

        # Construct the path to the Excel file
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")
        timestamp_path = os.path.join(data_dir, timestamp_id)
        output_dir = os.path.join(timestamp_path, "output")

        logger.info(f"Checking Excel availability for timestamp: {timestamp_id}")
        logger.info(f"Looking for Excel file in: {output_dir}")

        if not os.path.exists(output_dir):
            logger.error(f"Output directory not found: {output_dir}")
            return jsonify({'error': f'Output directory not found for timestamp {timestamp_id}', 'available': False}), 200

        # Find the Excel file
        excel_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.xlsx')]
        if not excel_files:
            logger.error(f"No Excel files found in directory: {output_dir}")
            return jsonify({'error': 'Excel file not found in the output directory', 'available': False, 'excelFileName': None}), 200

        excel_file_path = os.path.join(output_dir, excel_files[0])
        logger.info(f"Found Excel file: {excel_file_path}")

        # Verify the file exists and is readable
        if not os.path.isfile(excel_file_path):
            logger.error(f"Excel file does not exist: {excel_file_path}")
            return jsonify({'error': 'Excel file does not exist', 'available': False, 'excelFileName': None}), 200

        # Get the original PDF filename
        input_pdf_dir = os.path.join(timestamp_path, "inputPDF")
        pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
        original_filename = pdf_files[0] if pdf_files else "processed_statement.pdf"
        logger.info(f"Original PDF filename: {original_filename}")

        # Return success response
        return jsonify({
            'available': True,
            'excelFileName': excel_files[0],
            'excelPath': excel_file_path,
            'originalFileName': original_filename
        }), 200

    except Exception as e:
        logger.error(f"Error checking Excel availability: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e), 'available': False, 'excelFileName': None}), 500


@app.route('/api/download/<timestamp_id>', methods=['GET'])
def download_excel(timestamp_id):
    """
    API endpoint to download an Excel file from the history.

    Args:
        timestamp_id: The timestamp directory ID

    Returns:
        The Excel file for download
    """
    try:
        # Validate the timestamp ID
        if not timestamp_id or '..' in timestamp_id:
            logger.error(f"Invalid timestamp ID: {timestamp_id}")
            return jsonify({'error': 'Invalid timestamp ID'}), 400

        # Construct the path to the Excel file
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")
        timestamp_path = os.path.join(data_dir, timestamp_id)
        output_dir = os.path.join(timestamp_path, "output")

        logger.info(f"Download request for timestamp: {timestamp_id}")
        logger.info(f"Looking for Excel file in: {output_dir}")

        if not os.path.exists(output_dir):
            logger.error(f"Output directory not found: {output_dir}")
            return jsonify({'error': f'Output directory not found for timestamp {timestamp_id}'}), 404

        # Find the Excel file
        excel_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.xlsx')]
        if not excel_files:
            logger.error(f"No Excel files found in directory: {output_dir}")
            return jsonify({'error': 'Excel file not found in the output directory'}), 404

        excel_file_path = os.path.join(output_dir, excel_files[0])
        logger.info(f"Found Excel file: {excel_file_path}")

        # Verify the file exists and is readable
        if not os.path.isfile(excel_file_path):
            logger.error(f"Excel file does not exist: {excel_file_path}")
            return jsonify({'error': 'Excel file does not exist'}), 404

        # Get the original PDF filename
        input_pdf_dir = os.path.join(timestamp_path, "inputPDF")
        pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
        original_filename = pdf_files[0] if pdf_files else "processed_statement.pdf"
        logger.info(f"Original PDF filename: {original_filename}")

        # Return the Excel file for download
        download_name = f"{os.path.splitext(original_filename)[0]}.xlsx"
        logger.info(f"Sending file for download: {download_name}")

        try:
            # Read the file into memory
            with open(excel_file_path, 'rb') as f:
                file_data = f.read()

            # Create a response with the file data
            from flask import Response
            response = Response(
                file_data,
                status=200,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

            # Set headers to force download
            response.headers["Content-Disposition"] = f"attachment; filename={download_name}"
            response.headers["Content-Length"] = len(file_data)
            response.headers['Access-Control-Allow-Origin'] = '*'
            response.headers['Access-Control-Expose-Headers'] = 'Content-Disposition, Content-Length'
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

            logger.info(f"Successfully prepared file for download: {excel_file_path} with size {len(file_data)} bytes")
            return response
        except Exception as send_error:
            logger.error(f"Error sending file: {send_error}")
            logger.error(traceback.format_exc())
            return jsonify({'error': f'Error sending file: {str(send_error)}'}), 500

    except Exception as e:
        logger.error(f"Error downloading Excel: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@app.route('/api/download-pdf/<timestamp_id>', methods=['GET'])
def download_pdf(timestamp_id):
    """
    API endpoint to download the original PDF file from the history.

    Args:
        timestamp_id: The timestamp directory ID

    Returns:
        The PDF file for download
    """
    try:
        # Validate the timestamp ID
        if not timestamp_id or '..' in timestamp_id:
            logger.error(f"Invalid timestamp ID: {timestamp_id}")
            return jsonify({'error': 'Invalid timestamp ID'}), 400

        # Construct the path to the PDF file
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")
        timestamp_path = os.path.join(data_dir, timestamp_id)
        input_pdf_dir = os.path.join(timestamp_path, "inputPDF")

        logger.info(f"Download PDF request for timestamp: {timestamp_id}")
        logger.info(f"Looking for PDF file in: {input_pdf_dir}")

        if not os.path.exists(input_pdf_dir):
            logger.error(f"Input PDF directory not found: {input_pdf_dir}")
            return jsonify({'error': f'Input PDF directory not found for timestamp {timestamp_id}'}), 404

        # Find the PDF file
        pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
        if not pdf_files:
            logger.error(f"No PDF files found in directory: {input_pdf_dir}")
            return jsonify({'error': 'PDF file not found in the inputPDF directory'}), 404

        pdf_file_path = os.path.join(input_pdf_dir, pdf_files[0])
        logger.info(f"Found PDF file: {pdf_file_path}")

        # Verify the file exists and is readable
        if not os.path.isfile(pdf_file_path):
            logger.error(f"PDF file does not exist: {pdf_file_path}")
            return jsonify({'error': 'PDF file does not exist'}), 404

        # Return the PDF file for download
        download_name = pdf_files[0]
        logger.info(f"Sending PDF file for download: {download_name}")

        try:
            return send_file(
                pdf_file_path,
                as_attachment=True,
                download_name=download_name,
                mimetype='application/pdf'
            )
        except Exception as send_error:
            logger.error(f"Error sending PDF file: {send_error}")
            logger.error(traceback.format_exc())
            return jsonify({'error': f'Error sending PDF file: {str(send_error)}'}), 500

    except Exception as e:
        logger.error(f"Error downloading PDF: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
