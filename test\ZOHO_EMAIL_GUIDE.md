# Zoho Email Authentication Guide

This guide will help you troubleshoot and fix authentication issues with Zoho SMTP.

## Common Authentication Error

If you see an error like this:

```
SMTP Authentication Failed: (535, b'Authentication Failed')
```

This means that <PERSON><PERSON><PERSON> is rejecting your login credentials. Here's how to fix it:

## Solution 1: Generate an App Password

Zoho often requires an app-specific password for SMTP access, especially if you have two-factor authentication (2FA) enabled.

1. Log in to your Zoho Mail account
2. Go to Account Settings > Security
3. Under "App Passwords" or "Application-specific passwords", click "Generate New Password"
4. Give it a name like "Service Health Check"
5. Copy the generated password
6. Update your `config.json` file with this password

## Solution 2: Enable Less Secure Apps

If you don't have 2FA enabled, you might need to enable "less secure apps" access:

1. Log in to your Zoho Mail account
2. Go to Account Settings > Security
3. Look for "Access via less secure apps" or similar setting
4. Enable this option

## Solution 3: Check SMTP Settings

Make sure you're using the correct SMTP settings:

1. **Server**:
   - smtp.zoho.in (for Indian accounts)
   - smtp.zoho.com (for international accounts)
   - smtp.zoho.eu (for European accounts)

2. **Ports**:
   - 587 (TLS) - Most common
   - 465 (SSL) - Alternative
   - 25 (TLS) - Legacy, often blocked by ISPs

3. **Authentication**: Always required

## Solution 4: Sender Email Must Match Username

The sender email address must match the username you're using to authenticate:

```json
{
    "username": "<EMAIL>",
    "sender": "<EMAIL>"  // Must match username
}
```

## Testing Different Configurations

You can use the `test_email.py` script with different options to troubleshoot:

```bash
# Try all common configurations
python test_email.py --try-all

# Enable debug mode to see detailed SMTP communication
python test_email.py --debug

# Try SSL on port 465
python test_email.py --ssl --port 465
```

## Zoho Account Security

If you've tried all the above and still have issues:

1. Check if your account is locked due to too many failed attempts
2. Verify there are no IP restrictions on your account
3. Check if your organization has any security policies that might block SMTP access

## Contact Zoho Support

If you've tried everything and still can't get it to work, contact Zoho support:

https://www.zoho.com/mail/contact.html
