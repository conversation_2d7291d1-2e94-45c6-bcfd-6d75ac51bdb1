pipeline {
    agent any
    
    options {
        timeout(time: 10, unit: 'MINUTES')
        disableConcurrentBuilds()
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Setup Python Environment') {
            steps {
                script {
                    if (isUnix()) {
                        sh '''
                            python -m venv venv || python3 -m venv venv
                            . venv/bin/activate
                            pip install --upgrade pip
                            pip install requests
                        '''
                    } else {
                        bat '''
                            python -m venv venv
                            venv\\Scripts\\activate
                            pip install --upgrade pip
                            pip install requests
                        '''
                    }
                }
            }
        }
        
        stage('Run Health Check Tests') {
            steps {
                script {
                    if (isUnix()) {
                        sh '''
                            . venv/bin/activate
                            python -m unittest test/test_service_health.py
                        '''
                    } else {
                        bat '''
                            venv\\Scripts\\activate
                            python -m unittest test\\test_service_health.py
                        '''
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                if (isUnix()) {
                    sh 'rm -rf venv || true'
                } else {
                    bat 'rmdir /s /q venv || exit 0'
                }
            }
        }
        success {
            echo 'All services are running correctly!'
        }
        failure {
            echo 'One or more services are not running!'
            
            // You can add notification steps here, e.g., email, Slack, etc.
            // mail to: '<EMAIL>',
            //     subject: "Failed Pipeline: ${currentBuild.fullDisplayName}",
            //     body: "Something is wrong with the services. Check the build at ${env.BUILD_URL}"
        }
    }
}
