function e(e,t){for(var n=0;n<t.length;n++){const r=t[n];if("string"!=typeof r&&!Array.isArray(r))for(const t in r)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(r,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>r[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var n,r,o={exports:{}},a={};function i(){if(n)return a;n=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.consumer"),u=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),d=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,y={};function v(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||h}function g(){}function w(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},g.prototype=v.prototype;var b=w.prototype=new g;b.constructor=w,m(b,v.prototype),b.isPureReactComponent=!0;var E=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},x=Object.prototype.hasOwnProperty;function R(t,n,r,o,a,i){return r=i.ref,{$$typeof:e,type:t,key:n,ref:void 0!==r?r:null,props:i}}function C(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}var _=/\/+/g;function $(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function O(){}function T(n,r,o,a,i){var l=typeof n;"undefined"!==l&&"boolean"!==l||(n=null);var u,s,c=!1;if(null===n)c=!0;else switch(l){case"bigint":case"string":case"number":c=!0;break;case"object":switch(n.$$typeof){case e:case t:c=!0;break;case p:return T((c=n._init)(n._payload),r,o,a,i)}}if(c)return i=i(n),c=""===a?"."+$(n,0):a,E(i)?(o="",null!=c&&(o=c.replace(_,"$&/")+"/"),T(i,r,o,"",(function(e){return e}))):null!=i&&(C(i)&&(u=i,s=o+(null==i.key||n&&n.key===i.key?"":(""+i.key).replace(_,"$&/")+"/")+c,i=R(u.type,s,void 0,0,0,u.props)),r.push(i)),1;c=0;var f,h=""===a?".":a+":";if(E(n))for(var m=0;m<n.length;m++)c+=T(a=n[m],r,o,l=h+$(a,m),i);else if("function"==typeof(m=null===(f=n)||"object"!=typeof f?null:"function"==typeof(f=d&&f[d]||f["@@iterator"])?f:null))for(n=m.call(n),m=0;!(a=n.next()).done;)c+=T(a=a.value,r,o,l=h+$(a,m++),i);else if("object"===l){if("function"==typeof n.then)return T(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(O,O):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(n),r,o,a,i);throw r=String(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===r?"object with keys {"+Object.keys(n).join(", ")+"}":r)+"). If you meant to render a collection of children, use an array instead.")}return c}function k(e,t,n){if(null==e)return e;var r=[],o=0;return T(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function P(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function A(){}return a.Children={map:k,forEach:function(e,t,n){k(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return k(e,(function(){t++})),t},toArray:function(e){return k(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},a.Component=v,a.Fragment=r,a.Profiler=i,a.PureComponent=w,a.StrictMode=o,a.Suspense=c,a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,a.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},a.cache=function(e){return function(){return e.apply(null,arguments)}},a.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),o=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!x.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(r[a]=t[a]);var a=arguments.length-2;if(1===a)r.children=n;else if(1<a){for(var i=Array(a),l=0;l<a;l++)i[l]=arguments[l+2];r.children=i}return R(e.type,o,void 0,0,0,r)},a.createContext=function(e){return(e={$$typeof:u,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:l,_context:e},e},a.createElement=function(e,t,n){var r,o={},a=null;if(null!=t)for(r in void 0!==t.key&&(a=""+t.key),t)x.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var i=arguments.length-2;if(1===i)o.children=n;else if(1<i){for(var l=Array(i),u=0;u<i;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===o[r]&&(o[r]=i[r]);return R(e,a,void 0,0,0,o)},a.createRef=function(){return{current:null}},a.forwardRef=function(e){return{$$typeof:s,render:e}},a.isValidElement=C,a.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:P}},a.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},a.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),o=S.S;null!==o&&o(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(A,L)}catch(a){L(a)}finally{S.T=t}},a.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},a.use=function(e){return S.H.use(e)},a.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},a.useCallback=function(e,t){return S.H.useCallback(e,t)},a.useContext=function(e){return S.H.useContext(e)},a.useDebugValue=function(){},a.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},a.useEffect=function(e,t,n){var r=S.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},a.useId=function(){return S.H.useId()},a.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},a.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},a.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},a.useMemo=function(e,t){return S.H.useMemo(e,t)},a.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},a.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},a.useRef=function(e){return S.H.useRef(e)},a.useState=function(e){return S.H.useState(e)},a.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},a.useTransition=function(){return S.H.useTransition()},a.version="19.1.0",a}function l(){return r||(r=1,o.exports=i()),o.exports}var u=l();const s=t(u),c=e({__proto__:null,default:s},[u]);var f,p,d={exports:{}},h={};function m(){if(f)return h;f=1;var e=l();function t(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function n(){}var r={d:{f:n,r:function(){throw Error(t(522))},D:n,C:n,L:n,m:n,X:n,S:n,M:n},p:0,findDOMNode:null},o=Symbol.for("react.portal");var a=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function i(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}return h.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,h.createPortal=function(e,n){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(t(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,n,null,r)},h.flushSync=function(e){var t=a.T,n=r.p;try{if(a.T=null,r.p=2,e)return e()}finally{a.T=t,r.p=n,r.d.f()}},h.preconnect=function(e,t){"string"==typeof e&&(t?t="string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,r.d.C(e,t))},h.prefetchDNS=function(e){"string"==typeof e&&r.d.D(e)},h.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,o=i(n,t.crossOrigin),a="string"==typeof t.integrity?t.integrity:void 0,l="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?r.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:o,integrity:a,fetchPriority:l}):"script"===n&&r.d.X(e,{crossOrigin:o,integrity:a,fetchPriority:l,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},h.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=i(t.as,t.crossOrigin);r.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&r.d.M(e)},h.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,o=i(n,t.crossOrigin);r.d.L(e,n,{crossOrigin:o,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},h.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=i(t.as,t.crossOrigin);r.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else r.d.m(e)},h.requestFormReset=function(e){r.d.r(e)},h.unstable_batchedUpdates=function(e,t){return e(t)},h.useFormState=function(e,t,n){return a.H.useFormState(e,t,n)},h.useFormStatus=function(){return a.H.useHostTransitionStatus()},h.version="19.1.0",h}function y(){if(p)return d.exports;return p=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),d.exports=m(),d.exports}var v,g={};!function(){if(v)return g;v=1,Object.defineProperty(g,"__esModule",{value:!0}),g.parse=function(e,t){const n=new a,r=e.length;if(r<2)return n;const o=(null==t?void 0:t.decode)||u;let s=0;do{const t=e.indexOf("=",s);if(-1===t)break;const a=e.indexOf(";",s),u=-1===a?r:a;if(t>u){s=e.lastIndexOf(";",t-1)+1;continue}const c=i(e,s,t),f=l(e,t,c),p=e.slice(c,f);if(void 0===n[p]){let r=i(e,t+1,u),a=l(e,u,r);const s=o(e.slice(r,a));n[p]=s}s=u+1}while(s<r);return n},g.serialize=function(a,i,l){const u=(null==l?void 0:l.encode)||encodeURIComponent;if(!e.test(a))throw new TypeError(`argument name is invalid: ${a}`);const s=u(i);if(!t.test(s))throw new TypeError(`argument val is invalid: ${i}`);let c=a+"="+s;if(!l)return c;if(void 0!==l.maxAge){if(!Number.isInteger(l.maxAge))throw new TypeError(`option maxAge is invalid: ${l.maxAge}`);c+="; Max-Age="+l.maxAge}if(l.domain){if(!n.test(l.domain))throw new TypeError(`option domain is invalid: ${l.domain}`);c+="; Domain="+l.domain}if(l.path){if(!r.test(l.path))throw new TypeError(`option path is invalid: ${l.path}`);c+="; Path="+l.path}if(l.expires){if(!function(e){return"[object Date]"===o.call(e)}(l.expires)||!Number.isFinite(l.expires.valueOf()))throw new TypeError(`option expires is invalid: ${l.expires}`);c+="; Expires="+l.expires.toUTCString()}l.httpOnly&&(c+="; HttpOnly");l.secure&&(c+="; Secure");l.partitioned&&(c+="; Partitioned");if(l.priority){switch("string"==typeof l.priority?l.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${l.priority}`)}}if(l.sameSite){switch("string"==typeof l.sameSite?l.sameSite.toLowerCase():l.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${l.sameSite}`)}}return c};const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,a=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function i(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function l(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}();var w="popstate";function b(e={}){return function(e,t,n,r={}){let{window:o=document.defaultView,v5Compat:a=!1}=r,i=o.history,l="POP",u=null,s=c();null==s&&(s=0,i.replaceState({...i.state,idx:s},""));function c(){return(i.state||{idx:null}).idx}function f(){l="POP";let e=c(),t=null==e?null:e-s;s=e,u&&u({action:l,location:m.location,delta:t})}function p(e,t){l="PUSH";let n=R(m.location,e,t);s=c()+1;let r=x(n,s),f=m.createHref(n);try{i.pushState(r,"",f)}catch(p){if(p instanceof DOMException&&"DataCloneError"===p.name)throw p;o.location.assign(f)}a&&u&&u({action:l,location:m.location,delta:1})}function d(e,t){l="REPLACE";let n=R(m.location,e,t);s=c();let r=x(n,s),o=m.createHref(n);i.replaceState(r,"",o),a&&u&&u({action:l,location:m.location,delta:0})}function h(e){return function(e,t=!1){let n="http://localhost";"undefined"!=typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href);E(n,"No window.location.(origin|href) available to create URL");let r="string"==typeof e?e:C(e);r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r);return new URL(r,n)}(e)}let m={get action(){return l},get location(){return e(o,i)},listen(e){if(u)throw new Error("A history only accepts one active listener");return o.addEventListener(w,f),u=e,()=>{o.removeEventListener(w,f),u=null}},createHref:e=>t(o,e),createURL:h,encodeLocation(e){let t=h(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:p,replace:d,go:e=>i.go(e)};return m}((function(e,t){let{pathname:n,search:r,hash:o}=e.location;return R("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:C(t)}),0,e)}function E(e,t){if(!1===e||null==e)throw new Error(t)}function S(e,t){if(!e)try{throw new Error(t)}catch(n){}}function x(e,t){return{usr:e.state,key:e.key,idx:t}}function R(e,t,n=null,r){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?_(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function C({pathname:e="/",search:t="",hash:n=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function _(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function $(e,t,n="/"){return function(e,t,n,r){let o="string"==typeof t?_(t):t,a=I(o.pathname||"/",n);if(null==a)return null;let i=O(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(i);let l=null;for(let u=0;null==l&&u<i.length;++u){let e=F(a);l=H(i[u],e,r)}return l}(e,t,n,!1)}function O(e,t=[],n=[],r=""){let o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(E(i.relativePath.startsWith(r),`Absolute route path "${i.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(r.length));let l=V([r,i.relativePath]),u=n.concat(i);e.children&&e.children.length>0&&(E(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),O(e.children,t,u,l)),(null!=e.path||e.index)&&t.push({path:l,score:M(l,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&(null==(n=e.path)?void 0:n.includes("?")))for(let r of T(e.path))o(e,t,r);else o(e,t)})),t}function T(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===r.length)return o?[a,""]:[a];let i=T(r.join("/")),l=[];return l.push(...i.map((e=>""===e?a:[a,e].join("/")))),o&&l.push(...i),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var k=/^:[\w-]+$/,P=3,L=2,A=1,N=10,j=-2,D=e=>"*"===e;function M(e,t){let n=e.split("/"),r=n.length;return n.some(D)&&(r+=j),t&&(r+=L),n.filter((e=>!D(e))).reduce(((e,t)=>e+(k.test(t)?P:""===t?A:N)),r)}function H(e,t,n=!1){let{routesMeta:r}=e,o={},a="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],u=l===r.length-1,s="/"===a?t:t.slice(a.length)||"/",c=U({path:e.relativePath,caseSensitive:e.caseSensitive,end:u},s),f=e.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=U({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},s)),!c)return null;Object.assign(o,c.params),i.push({params:o,pathname:V([a,c.pathname]),pathnameBase:Y(V([a,c.pathnameBase])),route:f}),"/"!==c.pathnameBase&&(a=V([a,c.pathnameBase]))}return i}function U(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t=!1,n=!0){S("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))");let a=new RegExp(o,t?void 0:"i");return[a,r]}(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce(((e,{paramName:t,isOptional:n},r)=>{if("*"===t){let e=l[r]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const o=l[r];return e[t]=n&&!o?void 0:(o||"").replace(/%2F/g,"/"),e}),{}),pathname:a,pathnameBase:i,pattern:e}}function F(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return S(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function I(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function W(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function B(e){let t=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function z(e,t,n,r=!1){let o;"string"==typeof e?o=_(e):(o={...e},E(!o.pathname||!o.pathname.includes("?"),W("?","pathname","search",o)),E(!o.pathname||!o.pathname.includes("#"),W("#","pathname","hash",o)),E(!o.search||!o.search.includes("#"),W("#","search","hash",o)));let a,i=""===e||""===o.pathname,l=i?"/":o.pathname;if(null==l)a=n;else{let e=t.length-1;if(!r&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}a=e>=0?t[e]:"/"}let u=function(e,t="/"){let{pathname:n,search:r="",hash:o=""}="string"==typeof e?_(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:K(r),hash:q(o)}}(o,a),s=l&&"/"!==l&&l.endsWith("/"),c=(i||"."===l)&&n.endsWith("/");return u.pathname.endsWith("/")||!s&&!c||(u.pathname+="/"),u}var V=e=>e.join("/").replace(/\/\/+/g,"/"),Y=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),K=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",q=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";var G=["POST","PUT","PATCH","DELETE"];new Set(G);var J=["GET",...G];new Set(J);var X=u.createContext(null);X.displayName="DataRouter";var Q=u.createContext(null);Q.displayName="DataRouterState";var Z=u.createContext({isTransitioning:!1});Z.displayName="ViewTransition",u.createContext(new Map).displayName="Fetchers",u.createContext(null).displayName="Await";var ee=u.createContext(null);ee.displayName="Navigation";var te=u.createContext(null);te.displayName="Location";var ne=u.createContext({outlet:null,matches:[],isDataRoute:!1});ne.displayName="Route";var re=u.createContext(null);function oe(){return null!=u.useContext(te)}function ae(){return E(oe(),"useLocation() may be used only in the context of a <Router> component."),u.useContext(te).location}re.displayName="RouteError";var ie="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function le(e){u.useContext(ee).static||u.useLayoutEffect(e)}function ue(){let{isDataRoute:e}=u.useContext(ne);return e?function(){let{router:e}=function(e){let t=u.useContext(X);return E(t,me(e)),t}("useNavigate"),t=ye("useNavigate"),n=u.useRef(!1);return le((()=>{n.current=!0})),u.useCallback((async(r,o={})=>{S(n.current,ie),n.current&&("number"==typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...o}))}),[e,t])}():function(){E(oe(),"useNavigate() may be used only in the context of a <Router> component.");let e=u.useContext(X),{basename:t,navigator:n}=u.useContext(ee),{matches:r}=u.useContext(ne),{pathname:o}=ae(),a=JSON.stringify(B(r)),i=u.useRef(!1);return le((()=>{i.current=!0})),u.useCallback(((r,l={})=>{if(S(i.current,ie),!i.current)return;if("number"==typeof r)return void n.go(r);let u=z(r,JSON.parse(a),o,"path"===l.relative);null==e&&"/"!==t&&(u.pathname="/"===u.pathname?t:V([t,u.pathname])),(l.replace?n.replace:n.push)(u,l.state,l)}),[t,n,a,o,e])}()}function se(e,{relative:t}={}){let{matches:n}=u.useContext(ne),{pathname:r}=ae(),o=JSON.stringify(B(n));return u.useMemo((()=>z(e,JSON.parse(o),r,"path"===t)),[e,o,r,t])}function ce(e,t,n,r){var o;E(oe(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a,static:i}=u.useContext(ee),{matches:l}=u.useContext(ne),s=l[l.length-1],c=s?s.params:{},f=s?s.pathname:"/",p=s?s.pathnameBase:"/",d=s&&s.route;{let e=d&&d.path||"";ge(f,!d||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${f}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let h,m=ae();if(t){let e="string"==typeof t?_(t):t;E("/"===p||(null==(o=e.pathname)?void 0:o.startsWith(p)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${p}" but pathname "${e.pathname}" was given in the \`location\` prop.`),h=e}else h=m;let y=h.pathname||"/",v=y;if("/"!==p){let e=p.replace(/^\//,"").split("/");v="/"+y.replace(/^\//,"").split("/").slice(e.length).join("/")}let g=!i&&n&&n.matches&&n.matches.length>0?n.matches:$(e,{pathname:v});S(d||null!=g,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),S(null==g||void 0!==g[g.length-1].route.element||void 0!==g[g.length-1].route.Component||void 0!==g[g.length-1].route.lazy,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let w=function(e,t=[],n=null){if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,o=null==n?void 0:n.errors;if(null!=o){let e=r.findIndex((e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id])));E(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),r=r.slice(0,Math.min(r.length,e+1))}let a=!1,i=-1;if(n)for(let l=0;l<r.length;l++){let e=r[l];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(i=l),e.route.id){let{loaderData:t,errors:o}=n,l=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!o||void 0===o[e.route.id]);if(e.route.lazy||l){a=!0,r=i>=0?r.slice(0,i+1):[r[0]];break}}}return r.reduceRight(((e,l,s)=>{let c,f=!1,p=null,d=null;n&&(c=o&&l.route.id?o[l.route.id]:void 0,p=l.route.errorElement||pe,a&&(i<0&&0===s?(ge("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),f=!0,d=null):i===s&&(f=!0,d=l.route.hydrateFallbackElement||null)));let h=t.concat(r.slice(0,s+1)),m=()=>{let t;return t=c?p:f?d:l.route.Component?u.createElement(l.route.Component,null):l.route.element?l.route.element:e,u.createElement(he,{match:l,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(l.route.ErrorBoundary||l.route.errorElement||0===s)?u.createElement(de,{location:n.location,revalidation:n.revalidation,component:p,error:c,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}(g&&g.map((e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:V([p,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?p:V([p,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,r);return t&&w?u.createElement(te.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...h},navigationType:"POP"}},w):w}function fe(){let e=function(){var e;let t=u.useContext(re),n=function(e){let t=u.useContext(Q);return E(t,me(e)),t}("useRouteError"),r=ye("useRouteError");if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},a={padding:"2px 4px",backgroundColor:r},i=null;return i=u.createElement(u.Fragment,null,u.createElement("p",null,"💿 Hey developer 👋"),u.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",u.createElement("code",{style:a},"ErrorBoundary")," or"," ",u.createElement("code",{style:a},"errorElement")," prop on your route.")),u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),n?u.createElement("pre",{style:o},n):null,i)}u.createContext(null);var pe=u.createElement(fe,null),de=class extends u.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?u.createElement(ne.Provider,{value:this.props.routeContext},u.createElement(re.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function he({routeContext:e,match:t,children:n}){let r=u.useContext(X);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),u.createElement(ne.Provider,{value:e},n)}function me(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ye(e){let t=function(e){let t=u.useContext(ne);return E(t,me(e)),t}(e),n=t.matches[t.matches.length-1];return E(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}var ve={};function ge(e,t,n){t||ve[e]||(ve[e]=!0,S(!1,n))}function we(e){E(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function be({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:o,static:a=!1}){E(!oe(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),l=u.useMemo((()=>({basename:i,navigator:o,static:a,future:{}})),[i,o,a]);"string"==typeof n&&(n=_(n));let{pathname:s="/",search:c="",hash:f="",state:p=null,key:d="default"}=n,h=u.useMemo((()=>{let e=I(s,i);return null==e?null:{location:{pathname:e,search:c,hash:f,state:p,key:d},navigationType:r}}),[i,s,c,f,p,d,r]);return S(null!=h,`<Router basename="${i}"> is not able to match the URL "${s}${c}${f}" because it does not start with the basename, so the <Router> won't render anything.`),null==h?null:u.createElement(ee.Provider,{value:l},u.createElement(te.Provider,{children:t,value:h}))}function Ee({children:e,location:t}){return ce(Se(e),t)}function Se(e,t=[]){let n=[];return u.Children.forEach(e,((e,r)=>{if(!u.isValidElement(e))return;let o=[...t,r];if(e.type===u.Fragment)return void n.push.apply(n,Se(e.props.children,o));E(e.type===we,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),E(!e.props.index||!e.props.children,"An index route cannot have child routes.");let a={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(a.children=Se(e.props.children,o)),n.push(a)})),n}u.memo((function({routes:e,future:t,state:n}){return ce(e,void 0,n,t)}));var xe="get",Re="application/x-www-form-urlencoded";function Ce(e){return null!=e&&"string"==typeof e.tagName}var _e=null;var $e=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Oe(e){return null==e||$e.has(e)?e:(S(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Re}"`),null)}function Te(e,t){let n,r,o,a,i;if(Ce(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?I(i,t):null,n=e.getAttribute("method")||xe,o=Oe(e.getAttribute("enctype"))||Re,a=new FormData(e)}else if(function(e){return Ce(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Ce(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(r=l?I(l,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||xe,o=Oe(e.getAttribute("formenctype"))||Oe(i.getAttribute("enctype"))||Re,a=new FormData(i,e),!function(){if(null===_e)try{new FormData(document.createElement("form"),0),_e=!1}catch(e){_e=!0}return _e}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";a.append(`${e}x`,"0"),a.append(`${e}y`,"0")}else t&&a.append(t,r)}}else{if(Ce(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=xe,r=null,o=Re,i=e}var l;return a&&"text/plain"===o&&(i=a,a=void 0),{action:r,method:n.toLowerCase(),encType:o,formData:a,body:i}}function ke(e,t){if(!1===e||null==e)throw new Error(t)}function Pe(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function Le(e,t,n){return function(e,t){let n=new Set;return new Set(t),e.reduce(((e,t)=>{let r=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(t));return n.has(r)||(n.add(r),e.push({key:r,link:t})),e}),[])}((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter(Pe).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}function Ae(e,t,n,r,o,a){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null==(r=n[t].route.path)?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===a?t.filter(((e,t)=>i(e,t)||l(e,t))):"data"===a?t.filter(((t,a)=>{var u;let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,a)||l(t,a))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:(null==(u=n[0])?void 0:u.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof r)return r}return!0})):[]}function Ne(e,t,{includeHydrateFallback:n}={}){return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let o=[r.module];return r.clientActionModule&&(o=o.concat(r.clientActionModule)),r.clientLoaderModule&&(o=o.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(o=o.concat(r.hydrateFallbackModule)),r.imports&&(o=o.concat(r.imports)),o})).flat(1),[...new Set(r)];var r}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function je(){let e=u.useContext(X);return ke(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}var De=u.createContext(void 0);function Me(){let e=u.useContext(De);return ke(e,"You must render this element inside a <HydratedRouter> element"),e}function He(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Ue({page:e,...t}){let{router:n}=je(),r=u.useMemo((()=>$(n.routes,e,n.basename)),[n.routes,e,n.basename]);return r?u.createElement(Fe,{page:e,matches:r,...t}):null}function Fe({page:e,matches:t,...n}){let r=ae(),{manifest:o,routeModules:a}=Me(),{basename:i}=je(),{loaderData:l,matches:s}=function(){let e=u.useContext(Q);return ke(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}(),c=u.useMemo((()=>Ae(e,t,s,o,r,"data")),[e,t,s,o,r]),f=u.useMemo((()=>Ae(e,t,s,o,r,"assets")),[e,t,s,o,r]),p=u.useMemo((()=>{if(e===r.pathname+r.search+r.hash)return[];let n=new Set,u=!1;if(t.forEach((e=>{var t;let r=o.routes[e.route.id];r&&r.hasLoader&&(!c.some((t=>t.route.id===e.route.id))&&e.route.id in l&&(null==(t=a[e.route.id])?void 0:t.shouldRevalidate)||r.hasClientLoader?u=!0:n.add(e.route.id))})),0===n.size)return[];let s=function(e,t){let n="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===I(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}(e,i);return u&&n.size>0&&s.searchParams.set("_routes",t.filter((e=>n.has(e.route.id))).map((e=>e.route.id)).join(",")),[s.pathname+s.search]}),[i,l,r,o,c,t,e,a]),d=u.useMemo((()=>Ne(f,o)),[f,o]),h=function(e){let{manifest:t,routeModules:n}=Me(),[r,o]=u.useState([]);return u.useEffect((()=>{let r=!1;return Le(e,t,n).then((e=>{r||o(e)})),()=>{r=!0}}),[e,t,n]),r}(f);return u.createElement(u.Fragment,null,p.map((e=>u.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...n}))),d.map((e=>u.createElement("link",{key:e,rel:"modulepreload",href:e,...n}))),h.map((({key:e,link:t})=>u.createElement("link",{key:e,...t}))))}function Ie(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}De.displayName="FrameworkContext";var We="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{We&&(window.__reactRouterVersion="7.6.0")}catch(Xe){}function Be({basename:e,children:t,window:n}){let r=u.useRef();null==r.current&&(r.current=b({window:n,v5Compat:!0}));let o=r.current,[a,i]=u.useState({action:o.action,location:o.location}),l=u.useCallback((e=>{u.startTransition((()=>i(e)))}),[i]);return u.useLayoutEffect((()=>o.listen(l)),[o,l]),u.createElement(be,{basename:e,children:t,location:a.location,navigationType:a.action,navigator:o})}var ze=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ve=u.forwardRef((function({onClick:e,discover:t="render",prefetch:n="none",relative:r,reloadDocument:o,replace:a,state:i,target:l,to:s,preventScrollReset:c,viewTransition:f,...p},d){let h,{basename:m}=u.useContext(ee),y="string"==typeof s&&ze.test(s),v=!1;if("string"==typeof s&&y&&(h=s,We))try{let e=new URL(window.location.href),t=s.startsWith("//")?new URL(e.protocol+s):new URL(s),n=I(t.pathname,m);t.origin===e.origin&&null!=n?s=n+t.search+t.hash:v=!0}catch(Xe){S(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let g=function(e,{relative:t}={}){E(oe(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=u.useContext(ee),{hash:o,pathname:a,search:i}=se(e,{relative:t}),l=a;return"/"!==n&&(l="/"===a?n:V([n,a])),r.createHref({pathname:l,search:i,hash:o})}(s,{relative:r}),[w,b,x]=function(e,t){let n=u.useContext(De),[r,o]=u.useState(!1),[a,i]=u.useState(!1),{onFocus:l,onBlur:s,onMouseEnter:c,onMouseLeave:f,onTouchStart:p}=t,d=u.useRef(null);u.useEffect((()=>{if("render"===e&&i(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{i(e.isIntersecting)}))}),{threshold:.5});return d.current&&e.observe(d.current),()=>{e.disconnect()}}}),[e]),u.useEffect((()=>{if(r){let e=setTimeout((()=>{i(!0)}),100);return()=>{clearTimeout(e)}}}),[r]);let h=()=>{o(!0)},m=()=>{o(!1),i(!1)};return n?"intent"!==e?[a,d,{}]:[a,d,{onFocus:He(l,h),onBlur:He(s,m),onMouseEnter:He(c,h),onMouseLeave:He(f,m),onTouchStart:He(p,h)}]:[!1,d,{}]}(n,p),R=function(e,{target:t,replace:n,state:r,preventScrollReset:o,relative:a,viewTransition:i}={}){let l=ue(),s=ae(),c=se(e,{relative:a});return u.useCallback((u=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(u,t)){u.preventDefault();let t=void 0!==n?n:C(s)===C(c);l(e,{replace:t,state:r,preventScrollReset:o,relative:a,viewTransition:i})}}),[s,l,c,n,r,t,e,o,a,i])}(s,{replace:a,state:i,target:l,preventScrollReset:c,relative:r,viewTransition:f});let _=u.createElement("a",{...p,...x,href:h||g,onClick:v||o?e:function(t){e&&e(t),t.defaultPrevented||R(t)},ref:Ie(d,b),target:l,"data-discover":y||"render"!==t?void 0:"true"});return w&&!y?u.createElement(u.Fragment,null,_,u.createElement(Ue,{page:g})):_}));function Ye(e){let t=u.useContext(X);return E(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}Ve.displayName="Link",u.forwardRef((function({"aria-current":e="page",caseSensitive:t=!1,className:n="",end:r=!1,style:o,to:a,viewTransition:i,children:l,...s},c){let f=se(a,{relative:s.relative}),p=ae(),d=u.useContext(Q),{navigator:h,basename:m}=u.useContext(ee),y=null!=d&&function(e,t={}){let n=u.useContext(Z);E(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Ye("useViewTransitionState"),o=se(e,{relative:t.relative});if(!n.isTransitioning)return!1;let a=I(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=I(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=U(o.pathname,i)||null!=U(o.pathname,a)}(f)&&!0===i,v=h.encodeLocation?h.encodeLocation(f).pathname:f.pathname,g=p.pathname,w=d&&d.navigation&&d.navigation.location?d.navigation.location.pathname:null;t||(g=g.toLowerCase(),w=w?w.toLowerCase():null,v=v.toLowerCase()),w&&m&&(w=I(w,m)||w);const b="/"!==v&&v.endsWith("/")?v.length-1:v.length;let S,x=g===v||!r&&g.startsWith(v)&&"/"===g.charAt(b),R=null!=w&&(w===v||!r&&w.startsWith(v)&&"/"===w.charAt(v.length)),C={isActive:x,isPending:R,isTransitioning:y},_=x?e:void 0;S="function"==typeof n?n(C):[n,x?"active":null,R?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let $="function"==typeof o?o(C):o;return u.createElement(Ve,{...s,"aria-current":_,className:S,ref:c,style:$,to:a,viewTransition:i},"function"==typeof l?l(C):l)})).displayName="NavLink",u.forwardRef((({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:o,state:a,method:i=xe,action:l,onSubmit:s,relative:c,preventScrollReset:f,viewTransition:p,...d},h)=>{let m=function(){let{router:e}=Ye("useSubmit"),{basename:t}=u.useContext(ee),n=ye("useRouteId");return u.useCallback((async(r,o={})=>{let{action:a,method:i,encType:l,formData:u,body:s}=Te(r,t);if(!1===o.navigate){let t=o.fetcherKey||qe();await e.fetch(t,n,o.action||a,{preventScrollReset:o.preventScrollReset,formData:u,body:s,formMethod:o.method||i,formEncType:o.encType||l,flushSync:o.flushSync})}else await e.navigate(o.action||a,{preventScrollReset:o.preventScrollReset,formData:u,body:s,formMethod:o.method||i,formEncType:o.encType||l,replace:o.replace,state:o.state,fromRouteId:n,flushSync:o.flushSync,viewTransition:o.viewTransition})}),[e,t,n])}(),y=function(e,{relative:t}={}){let{basename:n}=u.useContext(ee),r=u.useContext(ne);E(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),a={...se(e||".",{relative:t})},i=ae();if(null==e){a.search=i.search;let e=new URLSearchParams(a.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();a.search=n?`?${n}`:""}}e&&"."!==e||!o.route.index||(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(a.pathname="/"===a.pathname?n:V([n,a.pathname]));return C(a)}(l,{relative:c}),v="get"===i.toLowerCase()?"get":"post",g="string"==typeof l&&ze.test(l);return u.createElement("form",{ref:h,method:v,action:y,onSubmit:r?s:e=>{if(s&&s(e),e.defaultPrevented)return;e.preventDefault();let r=e.nativeEvent.submitter,l=(null==r?void 0:r.getAttribute("formmethod"))||i;m(r||e.currentTarget,{fetcherKey:t,method:l,navigate:n,replace:o,state:a,relative:c,preventScrollReset:f,viewTransition:p})},...d,"data-discover":g||"render"!==e?void 0:"true"})})).displayName="Form";var Ke=0,qe=()=>`__${String(++Ke)}__`;var Ge=y();const Je=t(Ge);export{Be as B,c as R,s as a,Je as b,Ge as c,l as d,y as e,ae as f,Ee as g,we as h,u as r,ue as u};
